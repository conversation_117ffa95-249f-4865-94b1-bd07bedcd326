<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-07-14 15:11:40
 * @LastEditors: zhangqian <EMAIL>
 * @LastEditTime: 2025-07-31 16:46:31
 * @Description: prompt模板管理-列表页面
 * @FilePath: /dev-platform-ui/apps/web-ele/src/views/prompt/list.vue
-->
<script lang="ts" setup>
import type { VbenFormProps } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';
import type { PromptTemplate } from '#/api/prompt';

import { h } from 'vue';
import { useRouter } from 'vue-router';

import { Page } from '@vben/common-ui';
import { createIconifyIcon } from '@vben/icons';

import {
  ElButton,
  ElMessage,
  ElMessageBox,
  ElTag,
  ElTooltip,
} from 'element-plus';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
// 导入API接口
import { deletePromptTemplateApi, pagePromptTemplateApi } from '#/api/prompt';

import { formatDateTime, parseFlexibleVariables } from './utils';

// 扩展的表格数据类型，包含表格显示需要的字段
interface TablePromptTemplate extends Partial<PromptTemplate> {
  promptTemplateNo?: string;
  promptTemplateName?: string;
  promptTemplateDesc?: string;
  createEmpName?: string;
  createEmpCode?: string;
  createEmpDepartmentName?: string;
  createEmpDepartmentCode?: string;
  updatedTime?: string; // 更新时间
  variable?: string; // 接口返回的是字符串，需要解析为数组
  variables: string[]; // 解析后的变量数组
}

// 创建图标组件
const MdiPencil = createIconifyIcon('mdi:pencil');
const MdiHistory = createIconifyIcon('mdi:history');
const MdiChartLine = createIconifyIcon('mdi:chart-line');
const MdiDelete = createIconifyIcon('mdi:delete');
const MdiPlus = createIconifyIcon('mdi:plus');

const router = useRouter();

// 搜索表单配置
const formOptions: VbenFormProps = {
  collapsed: false,
  showCollapseButton: false,
  schema: [
    {
      component: 'Input',
      componentProps: {
        clearable: true,
        placeholder: '请输入模板名称',
      },
      fieldName: 'promptTemplateName',
      label: '模板名称',
    },
  ],
  submitButtonOptions: {
    content: '查询',
  },
  submitOnChange: false,
  submitOnEnter: false,
};

// 表格配置
const gridOptions: VxeGridProps<TablePromptTemplate> = {
  maxHeight: 660,
  showOverflow: false, // 关闭溢出隐藏，允许内容换行
  showHeaderOverflow: 'tooltip',
  rowConfig: {
    isHover: true,
  },
  scrollX: {
    enabled: true,
  },
  scrollY: {
    enabled: true,
  },
  columns: [
    {
      field: 'promptTemplateNo',
      title: '模板编号',
    },
    {
      field: 'promptTemplateName',
      title: '模板名称',
      width: 160,
    },
    {
      field: 'promptTemplateDesc',
      title: '应用场景',
      width: 220,
      slots: {
        default: ({ row }) => {
          return h('div', { class: 'py-2' }, [
            h(
              'span',
              {
                class: 'text-gray-700 leading-relaxed  ',
                style: {
                  wordWrap: 'break-word',
                  whiteSpace: 'normal',
                  lineHeight: '1.5',
                  display: 'block',
                },
              },
              row.promptTemplateDesc,
            ),
          ]);
        },
      },
    },
    {
      field: 'variables',
      title: '动态参数',
      slots: {
        default: ({ row }) => {
          const maxDisplay = 3; // 最多显示3个标签
          const variableTags = row.variables
            .slice(0, maxDisplay)
            .map((variable) =>
              h(
                ElTag,
                {
                  class: 'text-xs mr-1 mb-1',
                  key: variable,
                  size: 'small',
                  style: { fontSize: '11px' },
                },
                () => `{{${variable}}}`,
              ),
            );

          if (row.variables.length > maxDisplay) {
            const remainingCount = row.variables.length - maxDisplay;
            const remainingVariables = row.variables.slice(maxDisplay);

            variableTags.push(
              h(
                ElTooltip,
                {
                  placement: 'top',
                  showAfter: 300,
                  hideAfter: 100,
                  rawContent: true,
                },
                {
                  default: () =>
                    h(
                      ElTag,
                      {
                        class:
                          'text-xs cursor-help hover:bg-blue-50 transition-colors duration-200',
                        size: 'small',
                        type: 'info',
                        style: { fontSize: '11px' },
                      },
                      () => `+${remainingCount}`,
                    ),
                  content: () =>
                    h('div', { class: 'text-sm' }, [
                      h(
                        'div',
                        { class: 'font-medium mb-1' },
                        `还有 ${remainingCount} 个参数:`,
                      ),
                      ...remainingVariables.map((variable) =>
                        h(
                          ElTag,
                          {
                            class: 'text-xs mr-1 mb-1',
                            key: variable,
                            size: 'small',
                            style: { fontSize: '11px' },
                          },
                          () => `{{${variable}}}`,
                        ),
                      ),
                    ]),
                },
              ),
            );
          }

          // 如果没有参数，显示提示
          if (row.variables.length === 0) {
            return h(
              'div',
              { class: 'py-1' },
              h(
                ElTag,
                {
                  class: 'text-xs',
                  size: 'small',
                  type: 'info',
                  style: { fontSize: '11px' },
                },
                () => '无参数',
              ),
            );
          }

          return h(
            'div',
            {
              class: 'flex flex-wrap gap-1 py-1',
            },
            variableTags,
          );
        },
      },
    },
    {
      field: 'createdBy',
      title: '创建人',
      width: 140,
      slots: {
        default: ({ row }) => {
          // 拼接显示格式为"部门-姓名"
          const createEmpDepartmentName = row.createEmpDepartmentName || '';
          const createEmpName = row.createEmpName || '';

          // 拼接部门和姓名
          let displayText = '';
          if (createEmpDepartmentName && createEmpName) {
            displayText = `${createEmpDepartmentName}-${createEmpName}`;
          } else if (createEmpName) {
            displayText = `未知部门-${createEmpName}`;
          } else if (createEmpDepartmentName) {
            displayText = `${createEmpDepartmentName}-未知`;
          } else {
            displayText = '未知';
          }

          return h(
            'span',
            {
              class:
                createEmpDepartmentName && createEmpName
                  ? 'text-gray-700'
                  : 'text-gray-500',
              title: displayText,
            },
            displayText,
          );
        },
      },
    },
    {
      field: 'updatedTime',
      title: '更新时间',
      width: 180,
    },
    {
      title: '操作',
      width: 100,
      slots: {
        default: ({ row }) => {
          return h(
            'div',
            { class: 'flex gap-1 flex-wrap justify-center items-center py-1' },
            [
              h(
                ElButton,
                {
                  link: true,
                  onClick: () => editPrompt(row?.promptTemplateNo || ''),
                  size: 'small',
                  type: 'primary',
                },
                () => h(MdiPencil, { class: 'h-4 w-4' }),
              ),

              h(
                ElButton,
                {
                  link: true,
                  onClick: () => viewHistory(row?.promptTemplateNo || ''),
                  size: 'small',
                  type: 'info',
                },
                () => h(MdiHistory, { class: 'h-4 w-4' }),
              ),

              h(
                ElButton,
                {
                  link: true,
                  onClick: () => viewRecords(row?.promptTemplateNo || ''),
                  size: 'small',
                  type: 'success',
                },
                () => h(MdiChartLine, { class: 'h-4 w-4' }),
              ),

              h(
                ElButton,
                {
                  link: true,
                  onClick: () => deletePrompt(row?.promptTemplateNo || ''),
                  size: 'small',
                  type: 'danger',
                },
                () => h(MdiDelete, { class: 'h-4 w-4' }),
              ),
            ],
          );
        },
      },
    },
  ],
  keepSource: true,
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        try {
          // 使用真实API调用
          const { currentPage, pageSize } = page;
          const params = {
            promptTemplateName: formValues?.promptTemplateName, // 使用API对应的字段名
            currentPage,
            pageSize,
          };

          // 调用API获取数据
          const result = await pagePromptTemplateApi(params);
          // 将API返回的数据转换为表格需要的格式
          if (result) {
            const { data, totalCount } = result;
            // 将API返回的数据映射到表格需要的字段
            const items = data.map((item: TablePromptTemplate) => ({
              ...item,
              // 解析变量字符串为数组
              variables: parseFlexibleVariables(item?.variable || ''),
              // 更新时间格式化
              updatedTime: formatDateTime(item?.updatedTime || ''),
              // 其他可能需要的字段...
            }));
            return {
              items,
              total: totalCount,
            };
          }
          throw new Error('API返回数据格式不正确');
        } catch (error) {
          console.error('获取Prompt模板列表失败:', error);
          return {
            items: [],
            total: 0,
          };
        }
      },
    },
  },
  border: true,
};

const [Grid, gridApi] = useVbenVxeGrid({ formOptions, gridOptions });

// 操作方法
const createPrompt = () => {
  router.push('/prompt/add');
};

const editPrompt = (id: string) => {
  router.push(`/prompt/add?id=${id}`);
};

const viewHistory = (id: string) => {
  router.push(`/prompt/history?id=${id}`);
};

const viewRecords = (id: string) => {
  router.push(`/prompt/record?id=${id}`);
};

const deletePrompt = async (id: string) => {
  try {
    await ElMessageBox.confirm('确定要删除这个模板吗？', '确认删除', {
      type: 'warning',
    });
    try {
      await deletePromptTemplateApi({
        promptTemplateNo: id,
      });
      ElMessage.success('删除成功');
      gridApi.reload();
    } catch (error) {
      console.error('删除失败:', error);
    }
  } catch {
    // 用户取消删除
  }
};
</script>

<template>
  <Page
    prompt-template-desc="管理和配置AI助手的Prompt模板，支持动态参数和版本控制"
    title="Prompt模板管理"
  >
    <template #extra>
      <ElButton type="primary" @click="createPrompt">
        <MdiPlus class="mr-1 size-4" />
        创建模板
      </ElButton>
    </template>

    <Grid />
  </Page>
</template>

<style scoped></style>
