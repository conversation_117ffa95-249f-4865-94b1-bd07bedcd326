<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-07-14 15:11:40
 * @LastEditors: zhangqian <EMAIL>
 * @LastEditTime: 2025-07-30 09:43:03
 * @Description: prompt模板管理-调用记录
 * @FilePath: /dev-platform-ui/apps/web-ele/src/views/prompt/record.vue
-->
<script lang="ts" setup>
import type { VxeGridProps } from '#/adapter/vxe-table';
import type { PromptCallRecord, PromptStatistics } from '#/api/prompt';

import { computed, h, onMounted, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import { Page } from '@vben/common-ui';
import { createIconifyIcon } from '@vben/icons';

import dayjs from 'dayjs';
import {
  ElButton,
  ElCard,
  ElCol,
  ElDialog,
  ElIcon,
  ElMessage,
  ElOption,
  ElRow,
  ElSelect,
  ElSkeleton,
  ElTag,
} from 'element-plus';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  getPromptTemplateCallRecordApi,
  getPromptTemplateCallStatsApi,
  pagePromptTemplateCallRecordApi,
} from '#/api/prompt';

import { formatDateTime, getCallStatusInfo } from './utils';

const renderIfEmpty = (value: any, renderFn: () => any) => {
  if (value === null || value === undefined || value === '') {
    return h('span', { class: 'text-gray-400 text-sm' }, '-');
  }
  return renderFn();
};

// 创建图标组件
const MdiArrowLeft = createIconifyIcon('mdi:arrow-left');
const MdiEye = createIconifyIcon('mdi:eye');
const MdiChartLine = createIconifyIcon('mdi:chart-line');

const route = useRoute();
const router = useRouter();

// 获取路由参数中的模板ID
const promptId = computed(() => route.query.id as string);

// 响应式数据
const statistics = ref<PromptStatistics>({
  allCallTimes: 0,
  allConsumeToken: 0,
  avgResponseDuration: 0,
  promptTemplateNo: 0,
  todayCallTimes: 0,
});

// 时间过滤器
const timeFilter = ref<'ALL' | 'DAY' | 'MONTH' | 'WEEK'>('ALL');

// 详情弹窗
const detailDialogVisible = ref(false);
const currentRecord = ref<null | PromptCallRecord>(null);
const detailLoading = ref(false);
const loadPromptInfo = async (data: PromptCallRecord) => {
  if (!data?.callRecordNo) {
    ElMessage.error('缺少模板ID参数');
    return;
  }

  detailLoading.value = true;
  try {
    const result = await getPromptTemplateCallRecordApi({
      callRecordNo: data?.callRecordNo || '',
    });

    // 🔧 格式化时间字段，确保显示一致性
    currentRecord.value = {
      ...result,
      callTime: formatDateTime(result?.callTime || ''),
    };
  } catch (error) {
    console.error('获取模板信息失败:', error);
    ElMessage.error('获取调用详情失败');
  } finally {
    detailLoading.value = false;
  }
};

// 表格配置
const gridOptions: VxeGridProps<PromptCallRecord> = {
  maxHeight: 600,
  showOverflow: 'tooltip',
  showHeaderOverflow: 'tooltip',
  rowConfig: {
    isHover: true,
  },
  scrollX: {
    enabled: true,
  },
  scrollY: {
    enabled: true,
  },
  columns: [
    {
      field: 'callTime',
      title: '调用时间',
      width: 180,
      formatter: 'formatDateTime',
      // sortable: true,
    },
    {
      field: 'inputContent',
      title: '输入参数',
    },
    {
      field: 'responseDuration',
      title: '响应时间',
      width: 120,
      sortable: true,
      slots: {
        default: ({ row }) =>
          renderIfEmpty(row.responseDuration, () => {
            const isSlow = row.responseDuration && row.responseDuration > 3000;
            return h(
              'span',
              {
                class: isSlow
                  ? 'text-orange-500 font-medium'
                  : 'text-green-600 font-medium',
              },
              `${row.responseDuration}ms`,
            );
          }),
      },
    },
    {
      field: 'consumeToken',
      title: '消耗Token',
      width: 120,
      sortable: true,
      slots: {
        default: ({ row }) =>
          renderIfEmpty(row.consumeToken, () =>
            h(
              'span',
              { class: 'text-gray-600 text-sm' },
              `${row.consumeToken} tokens`,
            ),
          ),
      },
    },
    // SUCCESS -成功，FAILED -失败 , UNKNOWN -未知
    {
      field: 'callStatus',
      title: '状态',
      width: 100,
      slots: {
        default: ({ row }) =>
          renderIfEmpty(row.callStatus, () => {
            const { text, type } = getCallStatusInfo(row.callStatus);
            return h(ElTag, { size: 'small', type }, () => text);
          }),
      },
    },
    {
      title: '操作',
      width: 80,
      slots: {
        default: ({ row }) => {
          return h(
            ElButton,
            {
              link: true,
              size: 'small',
              type: 'primary',
              onClick: () => showDetail(row),
            },
            () => h(MdiEye, { class: 'h-4 w-4' }),
          );
        },
      },
    },
  ],
  keepSource: true,
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async ({ page }) => {
        try {
          if (!promptId.value) {
            throw new Error('缺少模板ID');
          }
          // 当 timeFilter.value === 'ALL' 不传参数callTime，而不是传''
          // 尝试使用真实API
          const result = await pagePromptTemplateCallRecordApi({
            recordDimension: timeFilter.value,
            callTime:
              timeFilter.value === 'ALL'
                ? undefined
                : dayjs().startOf('day').valueOf().toString(),
            currentPage: page.currentPage,
            pageSize: page.pageSize,
            promptTemplateNo: promptId.value,
          });
          const { data, totalCount } = result;
          const items = data.map((item: PromptCallRecord) => {
            return {
              ...item,
              callTime: formatDateTime(item?.callTime || ''),
            };
          });
          return {
            items,
            total: totalCount,
          };
        } catch (error) {
          console.error('获取调用记录失败，使用Mock数据:', error);
          return {
            items: [],
            total: 0,
          };
        }
      },
    },
  },
  border: true,
};

const [Grid, gridApi] = useVbenVxeGrid({ gridOptions });

const loadStatistics = async () => {
  if (!promptId.value) return;

  try {
    const result = await getPromptTemplateCallStatsApi({
      promptTemplateNo: promptId.value,
    });
    statistics.value = result;
  } catch (error) {
    console.error('获取统计信息失败，使用Mock数据:', error);
  }
};

const showDetail = (record: PromptCallRecord) => {
  // 清空之前的数据，确保显示正确的加载状态
  currentRecord.value = null;
  detailDialogVisible.value = true;
  loadPromptInfo(record);
};

const goBack = () => {
  router.push('/prompt/list');
};

const handleTimeFilterChange = () => {
  gridApi.reload();
};

// 生命周期
onMounted(() => {
  loadStatistics();
});
</script>

<template>
  <Page description="详细调用记录和统计信息" title="Prompt调用记录">
    <template #extra>
      <ElButton @click="goBack">
        <MdiArrowLeft class="mr-1 size-4" />
        返回列表
      </ElButton>
    </template>

    <!-- 统计卡片 -->
    <div class="mb-6">
      <ElRow :gutter="16">
        <ElCol :span="6">
          <ElCard class="text-center">
            <div class="mb-1 text-2xl font-bold text-blue-600">
              {{ statistics?.allCallTimes ?? 0 }}
            </div>
            <div class="text-sm text-gray-500">总调用次数</div>
          </ElCard>
        </ElCol>
        <ElCol :span="6">
          <ElCard class="text-center">
            <div class="mb-1 text-2xl font-bold text-green-600">
              {{ statistics?.avgResponseDuration }}ms
            </div>
            <div class="text-sm text-gray-500">平均响应时间</div>
          </ElCard>
        </ElCol>
        <ElCol :span="6">
          <ElCard class="text-center">
            <div class="mb-1 text-2xl font-bold text-purple-600">
              {{ statistics?.allConsumeToken?.toLocaleString() ?? 0 }}
            </div>
            <div class="text-sm text-gray-500">总消耗Token</div>
          </ElCard>
        </ElCol>
        <ElCol :span="6">
          <ElCard class="text-center">
            <div class="mb-1 text-2xl font-bold text-orange-600">
              {{ statistics?.todayCallTimes ?? 0 }}
            </div>
            <div class="text-sm text-gray-500">今日调用次数</div>
          </ElCard>
        </ElCol>
      </ElRow>
    </div>

    <!-- 调用记录表格 -->
    <ElCard>
      <template #header>
        <div class="flex items-center justify-between">
          <div class="flex items-center">
            <MdiChartLine class="mr-2 size-5 text-blue-600" />
            <span class="font-medium">调用记录详情</span>
          </div>
          <div class="flex items-center space-x-2">
            <span class="text-sm text-gray-500">时间维度筛选：</span>
            <ElSelect
              v-model="timeFilter"
              size="small"
              style="width: 120px"
              @change="handleTimeFilterChange"
            >
              <ElOption label="全部时间" value="ALL" />
              <ElOption label="今天" value="DAY" />
              <ElOption label="本周" value="WEEK" />
              <ElOption label="本月" value="MONTH" />
            </ElSelect>
          </div>
        </div>
      </template>

      <Grid />
    </ElCard>

    <!-- 详情弹窗 -->
    <ElDialog
      v-model="detailDialogVisible"
      title="调用详情"
      width="90%"
      :close-on-click-modal="false"
    >
      <!-- Loading 状态 -->
      <div v-if="detailLoading" class="space-y-4">
        <ElRow :gutter="16">
          <ElCol :span="12">
            <div class="rounded-lg border p-4">
              <h6
                class="mb-3 flex items-center text-sm font-medium text-gray-700"
              >
                <span class="mr-2 h-2 w-2 rounded-full bg-blue-500"></span>
                输入内容
              </h6>
              <div class="max-h-60 overflow-y-auto rounded bg-gray-50 p-3">
                <ElSkeleton :rows="2" animated />
              </div>
            </div>
          </ElCol>
          <ElCol :span="12">
            <div class="rounded-lg border p-4">
              <h6
                class="mb-3 flex items-center text-sm font-medium text-gray-700"
              >
                <span class="mr-2 h-2 w-2 rounded-full bg-green-500"></span>
                输出内容
              </h6>
              <div class="max-h-60 overflow-y-auto rounded bg-gray-50 p-3">
                <ElSkeleton :rows="2" animated />
              </div>
            </div>
          </ElCol>
        </ElRow>

        <div class="rounded-lg border p-4">
          <h6 class="mb-3 flex items-center text-sm font-medium text-gray-700">
            <span class="mr-2 h-2 w-2 rounded-full bg-purple-500"></span>
            调用信息
          </h6>
          <ElRow :gutter="16" class="text-sm">
            <ElCol :span="6">
              <div class="text-gray-500">调用时间：</div>
              <ElSkeleton :rows="1" animated />
            </ElCol>
            <ElCol :span="6">
              <div class="text-gray-500">响应时间：</div>
              <ElSkeleton :rows="1" animated />
            </ElCol>
            <ElCol :span="6">
              <div class="text-gray-500">消耗Token：</div>
              <ElSkeleton :rows="1" animated />
            </ElCol>
            <ElCol :span="6">
              <div class="text-gray-500">调用状态：</div>
              <ElSkeleton :rows="1" animated />
            </ElCol>
          </ElRow>
        </div>
      </div>

      <!-- 数据内容 -->
      <div v-else-if="currentRecord" class="space-y-4">
        <ElRow :gutter="16">
          <ElCol :span="12">
            <div class="rounded-lg border p-4">
              <h6
                class="mb-3 flex items-center text-sm font-medium text-gray-700"
              >
                <span class="mr-2 h-2 w-2 rounded-full bg-blue-500"></span>
                输入内容
              </h6>
              <div class="max-h-60 overflow-y-auto rounded bg-gray-50 p-3">
                <div
                  class="whitespace-pre-wrap font-mono text-sm leading-relaxed text-gray-800"
                >
                  {{ currentRecord.inputContent }}
                </div>
              </div>
            </div>
          </ElCol>
          <ElCol :span="12">
            <div class="rounded-lg border p-4">
              <h6
                class="mb-3 flex items-center text-sm font-medium text-gray-700"
              >
                <span class="mr-2 h-2 w-2 rounded-full bg-green-500"></span>
                输出内容
              </h6>
              <div class="max-h-60 overflow-y-auto rounded bg-gray-50 p-3">
                <div
                  class="whitespace-pre-wrap font-mono text-sm leading-relaxed text-gray-800"
                >
                  {{ currentRecord.outputContent }}
                </div>
              </div>
            </div>
          </ElCol>
        </ElRow>

        <div class="rounded-lg border p-4">
          <h6 class="mb-3 flex items-center text-sm font-medium text-gray-700">
            <span class="mr-2 h-2 w-2 rounded-full bg-purple-500"></span>
            调用信息
          </h6>
          <ElRow :gutter="16" class="text-sm">
            <ElCol :span="6">
              <div class="text-gray-500">调用时间：</div>
              <div class="font-medium">
                {{ currentRecord?.callTime || '--' }}
              </div>
            </ElCol>
            <ElCol :span="6">
              <div class="text-gray-500">响应时间：</div>
              <div
                class="font-medium"
                :class="
                  currentRecord?.responseDuration &&
                  currentRecord?.responseDuration > 3000
                    ? 'text-orange-500'
                    : 'text-green-600'
                "
              >
                {{
                  currentRecord.responseDuration
                    ? `${currentRecord.responseDuration}ms`
                    : '--'
                }}
              </div>
            </ElCol>
            <ElCol :span="6">
              <div class="text-gray-500">消耗Token：</div>
              <div class="font-medium">
                {{
                  currentRecord.consumeToken
                    ? `${currentRecord.consumeToken}tokens`
                    : '--'
                }}
              </div>
            </ElCol>
            <ElCol :span="6">
              <div class="text-gray-500">调用状态</div>
              <ElTag :type="getCallStatusInfo(currentRecord?.callStatus).type">
                {{ getCallStatusInfo(currentRecord?.callStatus).text }}
              </ElTag>
            </ElCol>
          </ElRow>
        </div>
      </div>

      <!-- 空状态 -->
      <div
        v-else
        class="flex flex-col items-center justify-center py-16 text-gray-500"
      >
        <ElIcon class="mb-4 text-4xl">
          <MdiEye />
        </ElIcon>
        <p>暂无调用详情数据</p>
      </div>
    </ElDialog>
  </Page>
</template>

<style scoped>
/* 统计卡片样式 */
:deep(.el-card) {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
}

:deep(.el-card:hover) {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

/* 表格样式 */
:deep(.vxe-table) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.vxe-table .vxe-header--row) {
  background-color: #f8f9fa;
}

/* 弹窗样式 */
:deep(.el-dialog) {
  border-radius: 12px;
}

:deep(.el-dialog__header) {
  border-bottom: 1px solid #f0f0f0;
  padding: 20px 24px 16px;
}

:deep(.el-dialog__body) {
  padding: 20px 24px;
}

/* 代码块样式 */
.font-mono {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

/* Loading 动画 */
.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Skeleton 动画增强 */
:deep(.el-skeleton__item) {
  background: linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 50%, #f2f2f2 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}
</style>
