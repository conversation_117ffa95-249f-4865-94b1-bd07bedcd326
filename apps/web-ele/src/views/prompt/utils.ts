/*
 * @Author: <PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-07-21 16:45:31
 * @LastEditors: zhang<PERSON>an <EMAIL>
 * @LastEditTime: 2025-07-30 09:37:21
 * @Description: 页面功能描述，例如：用户列表、商品详情等
 * @FilePath: /dev-platform-ui/apps/web-ele/src/views/prompt/utils.ts
 */
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';

import 'dayjs/locale/zh-cn';

// 配置 dayjs 插件
dayjs.extend(relativeTime);
dayjs.locale('zh-cn');
// 辅助函数：解析变量字符串为数组
export const parseVariables = (variableStr: string): string[] => {
  if (!variableStr) return [];

  // 使用正则表达式匹配 {{变量名}} 格式
  const matches = variableStr.match(/\{\{([^}]+)\}\}/g);
  if (!matches) return [];

  // 提取变量名（去掉大括号）
  return matches.map((match) => match.replaceAll(/[{}]/g, ''));
};

// 辅助函数：将变量数组转换为字符串
export const stringifyVariables = (input: Set<string> | string[]): string => {
  // 转成数组（如果是 Set）
  const arr = Array.isArray(input) ? input : [...input];

  // 去除空字符串、格式化为 {{变量}} 形式
  const wrapped = arr
    .map((item) => item.trim())
    .filter(Boolean)
    .map((item) => `{{${item}}}`);

  return wrapped.join(',');
};

// 辅助函数：解析变量字符串为数组 （字符串提取数据转为数组）
export const parseFlexibleVariables = (input: string): string[] => {
  if (!input) return [];

  // 优先尝试匹配 {{var}} 格式
  const templateMatches = [...input.matchAll(/\{\{(\w+)\}\}/g)]
    .map((m) => m[1])
    .filter((v): v is string => typeof v === 'string');

  if (templateMatches.length > 0) {
    return [...new Set(templateMatches)];
  }

  return [];
};

/**
 * 辅助函数：格式化时间（支持多种时间格式）
 * @param time 时间值，支持：时间戳(数字)、ISO字符串、Date对象等
 * @param format 格式化模板，默认 'YYYY-MM-DD HH:mm:ss'
 * @returns 格式化后的时间字符串
 * @example
 * formatTimesFun(1642204800000) // '2022-01-15 08:00:00'
 * formatTimesFun('1642204800000') // '2022-01-15 08:00:00' (字符串时间戳)
 * formatTimesFun('2022-01-15T08:00:00Z') // '2022-01-15 16:00:00'
 * formatTimesFun(new Date()) // '2022-01-15 16:30:45'
 * formatTimesFun(1642204800000, 'YYYY年MM月DD日') // '2022年01月15日'
 */
export const formatTimesFun = (
  time: Date | null | number | string | undefined,
  format: string = 'YYYY-MM-DD HH:mm:ss',
): string => {
  if (!time) return '';

  try {
    let processedTime: Date | number | string = time;

    // 特殊处理：如果是字符串且看起来像时间戳，转换为数字
    if (typeof time === 'string') {
      const numTime = Number(time);
      // 检查是否为有效的时间戳（通常是10位或13位数字）
      if (!Number.isNaN(numTime) && /^\d{10,13}$/.test(time.trim())) {
        processedTime = numTime;
      }
      // 否则保持原字符串，让 dayjs 尝试解析其他格式
    }

    // 使用 dayjs 处理各种时间格式
    const dayjsInstance = dayjs(processedTime);

    // 检查时间是否有效
    if (!dayjsInstance.isValid()) {
      console.warn('Invalid time format:', time);
      return '';
    }

    return dayjsInstance.format(format);
  } catch (error) {
    console.error('Time formatting error:', error, 'Input:', time);
    return '';
  }
};

/** 格式化为日期：2022-01-15 */
export const formatDate = (
  time: Date | null | number | string | undefined,
): string => formatTimesFun(time, 'YYYY-MM-DD');

/** 格式化为完整日期时间：2022-01-15 16:30:45 */
export const formatDateTime = (
  time: Date | null | number | string | undefined,
): string => formatTimesFun(time, 'YYYY-MM-DD HH:mm:ss');

/** 格式化为时间：16:30:45 */
export const formatTime = (
  time: Date | null | number | string | undefined,
): string => formatTimesFun(time, 'HH:mm:ss');

/** 格式化为简短日期时间：01-15 16:30 */
export const formatDateTimeShort = (
  time: Date | null | number | string | undefined,
): string => formatTimesFun(time, 'MM-DD HH:mm');

/** 格式化为相对时间：3分钟前、2小时前、1天前等 */
export const formatRelativeTime = (
  time: Date | null | number | string | undefined,
): string => {
  if (!time) return '';

  try {
    const dayjsInstance = dayjs(time);
    if (!dayjsInstance.isValid()) return '';

    return dayjsInstance.fromNow();
  } catch (error) {
    console.error('Relative time formatting error:', error);
    return '';
  }
};

/** 调用状态 */
export const callStatusMap: Record<
  string,
  { text: string; type: 'danger' | 'info' | 'success' | 'warning' }
> = {
  SUCCESS: { text: '成功', type: 'success' },
  FAILED: { text: '失败', type: 'danger' },
  UNKNOWN: { text: '未知', type: 'warning' },
};
export type CallStatusType = keyof typeof callStatusMap;

export const getCallStatusInfo = (
  status?: string,
): { text: string; type: 'danger' | 'info' | 'success' | 'warning' } => {
  const map = {
    SUCCESS: { text: '成功', type: 'success' },
    FAILED: { text: '失败', type: 'danger' },
    UNKNOWN: { text: '未知', type: 'warning' },
  } as const;

  if (status && status in map) {
    return map[status as keyof typeof map];
  }

  return { text: '-', type: 'info' };
};
