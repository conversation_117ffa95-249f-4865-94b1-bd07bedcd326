<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-07-14 15:11:40
 * @LastEditors: zhangqian <EMAIL>
 * @LastEditTime: 2025-07-28 16:40:03
 * @Description: prompt模板创建/编辑页面
 * @FilePath: /dev-platform-ui/apps/web-ele/src/views/prompt/add.vue
-->

<script lang="ts" setup>
import { computed, onMounted, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import { Page } from '@vben/common-ui';
import { useTabs } from '@vben/hooks';
import { ArrowLeft, Check, X } from '@vben/icons';

import { useDebounceFn } from '@vueuse/core';
import { ElButton, ElCard, ElCol, ElMessage, ElRow, ElTag } from 'element-plus';

import { useVbenForm, z } from '#/adapter/form';
import {
  createPromptTemplateApi,
  getPromptTemplateByNoApi,
  updatePromptTemplateApi,
} from '#/api/prompt';

import { parseFlexibleVariables, stringifyVariables } from './utils';

// 定义 Prompt 模板类型
interface PromptTemplate {
  promptTemplateNo?: string;
  promptTemplateName: string;
  promptTemplateDesc: string;
  systemPrompt: string; // 系统角色设定提示词
  userPrompt: string; // 任务模版用户提示词
  variable: string; // 变量字符串
}

const route = useRoute();
const router = useRouter();
const { setTabTitle, closeCurrentTab } = useTabs();

// 编辑模式判断
const isEdit = computed(() => !!route.query.id);
const pageTitle = computed(() => (isEdit.value ? '编辑模板' : '创建模板'));

// 动态参数
const extractedVariables = ref<string[]>([]);

// 提交函数
async function handleSubmit() {
  try {
    // 验证两个表单
    const basicValidResult = await basicFormApi.validate();
    const promptValidResult = await promptFormApi.validate();

    // 检查校验结果的 valid 属性
    if (!basicValidResult.valid || !promptValidResult.valid) {
      ElMessage.error('请检查表单填写是否正确');
      return;
    }

    // 获取表单数据
    const basicValues = await basicFormApi.getValues();
    const promptValues = await promptFormApi.getValues();

    const allValues = { ...basicValues, ...promptValues };
    const promptData = buildPromptData(allValues);

    // 根据模式调用不同的API
    await (isEdit.value
      ? updatePromptTemplateFun(promptData)
      : createPromptTemplateFun(promptData));

    ElMessage.success(isEdit.value ? '模板更新成功！' : '模板创建成功！');

    // 清空表单
    basicFormApi.resetForm();
    promptFormApi.resetForm();
    // 先关闭当前标签页
    await closeCurrentTab();

    // 然后跳转到列表页面
    await router.push('/prompt/list');
  } catch (error) {
    console.error('保存失败:', error);
    ElMessage.error(
      isEdit.value ? '模板更新失败，请重试' : '模板创建失败，请重试',
    );
  }
}

// 防抖保存函数（用于操作按钮）
const debouncedSubmit = useDebounceFn(handleSubmit, 300);

// 基本信息表单
const [BasicForm, basicFormApi] = useVbenForm({
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
  },
  showDefaultActions: false, // 不显示默认的提交和重置按钮
  layout: 'vertical',
  schema: [
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入模板名称',
      },
      fieldName: 'promptTemplateName',
      label: '模板名称',
      rules: z
        .string()
        .min(1, { message: '请输入模板名称' })
        .max(20, { message: '模板名称不能超过20个字符' }),
    },
    {
      component: 'Input',
      componentProps: {
        allowClear: true,
        showWordlimit: true,
        type: 'textarea',
        placeholder: '请输入应用场景描述',
        rows: 4,
        maxlength: 5000,
      },
      defaultValue: '',
      fieldName: 'promptTemplateDesc',
      label: '应用场景描述',
      rules: z.string().min(1, { message: '请输入应用场景描述' }),
    },
  ],
  wrapperClass: 'grid-cols-1',
});

// Prompt配置表单
const [PromptForm, promptFormApi] = useVbenForm({
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
  },
  showDefaultActions: false, // 不显示默认的提交和重置按钮
  layout: 'vertical',
  // 监听表单值变化
  handleValuesChange: async (values) => {
    const text = `${values.systemPrompt || ''} ${values.userPrompt || ''}`;
    extractedVariables.value = parseFlexibleVariables(text);
  },
  schema: [
    {
      component: 'Input',
      componentProps: {
        allowClear: true,
        type: 'textarea',
        rows: 4,
        placeholder: '定义AI助手的专业角色和职责...',
        maxlength: 5000,
      },
      defaultValue: '',
      fieldName: 'systemPrompt',
      label: '系统角色设定',
      help: '设定AI的专业身份、知识背景和行为准则',
    },
    {
      component: 'Input',
      componentProps: {
        allowClear: true,
        type: 'textarea',
        rows: 4,
        placeholder: '输入具体任务描述，使用 {{变量名}} 格式定义动态参数...',
        maxlength: 5000,
      },
      defaultValue: '',
      fieldName: 'userPrompt',
      label: '任务模板',
      help: '定义具体的业务任务，可使用 {{变量名}} 格式创建参数化模板',
    },
  ],
  wrapperClass: 'grid-cols-1',
});

const createPromptTemplateFun = async (data: Partial<PromptTemplate>) => {
  try {
    const result = await createPromptTemplateApi(data);
    return result;
  } catch (error) {
    console.error('创建模板失败:', error);
    throw error;
  }
};

const updatePromptTemplateFun = async (data: Partial<PromptTemplate>) => {
  try {
    const result = await updatePromptTemplateApi(data);
    return result;
  } catch (error) {
    console.error('更新模板失败:', error);
    throw error;
  }
};

const getPromptTemplateByNoFun = async (data: string) => {
  try {
    const result = await getPromptTemplateByNoApi({ promptTemplateNo: data });
    return result;
  } catch (error) {
    console.error('获取模板失败:', error);
    throw error;
  }
};

// 添加一个辅助函数来更新动态参数
const updateExtractedVariables = async () => {
  try {
    const promptValues = await promptFormApi.getValues();
    const text = `${promptValues.systemPrompt || ''} ${promptValues.userPrompt || ''}`;
    extractedVariables.value = parseFlexibleVariables(text);
  } catch {
    // 忽略错误
  }
};

// 加载数据（编辑模式）
onMounted(async () => {
  // 设置标签页标题
  await setTabTitle(pageTitle.value);

  if (isEdit.value) {
    const queryId = route.query.id as string;
    const result = await getPromptTemplateByNoFun(queryId);
    // 设置基本信息表单值
    basicFormApi.setValues({
      promptTemplateName: result.promptTemplateName,
      promptTemplateDesc: result.promptTemplateDesc,
    });
    // 设置Prompt配置表单值
    promptFormApi.setValues({
      systemPrompt: result.systemPrompt,
      userPrompt: result.userPrompt,
    });

    // 手动更新动态参数（因为表单值设置后watch可能不会立即触发）
    const text = `${result.systemPrompt || ''} ${result.userPrompt || ''}`;
    extractedVariables.value = parseFlexibleVariables(text);

    // 延迟再次更新，确保表单值已经完全设置
    setTimeout(async () => {
      await updateExtractedVariables();
    }, 100);
  }
});

// 构建请求数据
const buildPromptData = (values: Record<string, any>): PromptTemplate => {
  const baseData = {
    promptTemplateName: values.promptTemplateName,
    promptTemplateDesc: values.promptTemplateDesc,
    systemPrompt: values.systemPrompt,
    userPrompt: values.userPrompt,
    variable: stringifyVariables(extractedVariables.value), // 数组转字符串
  };

  if (isEdit.value) {
    return {
      ...baseData,
      promptTemplateNo: route.query.id as string,
    };
  }

  return baseData;
};

// 返回列表
const goBack = async () => {
  // 清空表单
  basicFormApi.resetForm();
  promptFormApi.resetForm();
  // 先关闭当前标签页
  await closeCurrentTab();

  // 然后跳转到列表页面
  await router.push('/prompt/list');
};
</script>

<template>
  <Page :title="pageTitle" description="配置AI助手的Prompt模板和动态参数">
    <template #extra>
      <ElButton @click="goBack">
        <ArrowLeft class="mr-1 size-4" />
        返回列表
      </ElButton>
    </template>
    <ElRow :gutter="24">
      <ElCol :lg="16" :span="24">
        <!-- 基本信息 -->
        <ElCard class="mb-4">
          <template #header>
            <div class="card-header">
              <span>基本信息</span>
            </div>
          </template>
          <BasicForm />
        </ElCard>

        <!-- Prompt配置 -->
        <ElCard>
          <template #header>
            <div class="card-header">
              <span>Prompt配置</span>
            </div>
          </template>
          <PromptForm />
        </ElCard>
      </ElCol>

      <ElCol :lg="8" :span="24">
        <ElCard class="mb-4">
          <template #header>
            <div class="card-header flex items-center justify-between">
              <span>动态参数</span>
              <span class="text-sm font-normal text-gray-500">
                系统会自动识别 &#123;&#123;变量名&#125;&#125; 格式的动态参数
              </span>
            </div>
          </template>

          <div v-if="extractedVariables.length > 0" class="space-y-2">
            <div class="flex flex-wrap gap-2">
              <ElTag
                v-for="variable in extractedVariables"
                :key="variable"
                type="success"
                size="small"
              >
                &#123;&#123;{{ variable }}&#125;&#125;
              </ElTag>
            </div>
          </div>
          <div v-else class="text-sm text-gray-500">暂无参数</div>
        </ElCard>

        <ElCard>
          <template #header>
            <div class="card-header">
              <span>操作</span>
            </div>
          </template>
          <div class="space-y-3">
            <ElButton
              type="primary"
              size="large"
              class="w-full"
              @click="debouncedSubmit"
            >
              <Check class="mr-1 size-4" />
              保存模板
            </ElButton>
            <ElButton size="large" class="w-full" @click="goBack">
              <X class="mr-1 size-4" />
              取消
            </ElButton>
          </div>
        </ElCard>
      </ElCol>
    </ElRow>
  </Page>
</template>
