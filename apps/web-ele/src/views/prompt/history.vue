<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-07-14 15:11:40
 * @LastEditors: zhangqian <EMAIL>
 * @LastEditTime: 2025-07-31 17:09:26
 * @Description: prompt模板管理-版本历史
 * @FilePath: /dev-platform-ui/apps/web-ele/src/views/prompt/history.vue
-->
<script lang="ts" setup>
import type { PromptVersion } from '#/api/prompt';

import { computed, onMounted, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import { Page } from '@vben/common-ui';
import { createIconifyIcon } from '@vben/icons';

import {
  ElButton,
  ElCard,
  ElCol,
  ElMessage,
  ElMessageBox,
  ElPagination,
  ElRow,
  ElTag,
} from 'element-plus';

import {
  getPromptTemplateVersionApi,
  pagePromptTemplateVersionApi,
  rollBackPromptTemplateVersionApi,
} from '#/api/prompt';

import { formatDateTime, parseFlexibleVariables } from './utils';

// 创建图标组件
const MdiArrowLeft = createIconifyIcon('mdi:arrow-left');
const MdiHistory = createIconifyIcon('mdi:history');
const MdiUserMd = createIconifyIcon('mdi:account-tie');
const MdiTasks = createIconifyIcon('mdi:format-list-bulleted');
const MdiTags = createIconifyIcon('mdi:tag-multiple');
// const MdiRestore = createIconifyIcon('mdi:restore');

const route = useRoute();
const router = useRouter();

// 获取路由参数中的模板ID
const promptId = computed(() => route.query.id as string);

// 响应式数据
const promptName = ref('');
const versions = ref<PromptVersion[]>([]);
const selectedVersion = ref<null | PromptVersion>(null);
const loading = ref(false);
const firstVersion = ref<null | PromptVersion>(null); // 版本列表第一页第一项
// 分页相关状态
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);

// 获取模板名称 获取列表之后 获取当前选择的版本的具体信息
const loadPromptInfo = async (version: PromptVersion) => {
  if (!version?.promptTemplateNo) {
    ElMessage.error('缺少模板ID参数');
    return;
  }
  try {
    const result = await getPromptTemplateVersionApi({
      dataVersion: version?.dataVersion || 0,
      promptTemplateNo: version?.promptTemplateNo || '',
    });
    promptName.value = result.promptTemplateName;
    const variables = parseFlexibleVariables(result?.variable);
    selectedVersion.value = {
      ...result,
      variables,
    };
  } catch (error) {
    console.error('获取模板信息失败:', error);
  }
};

// 加载版本历史
const loadVersions = async (page: number = currentPage.value) => {
  if (!promptId.value) return;

  loading.value = true;
  try {
    const result = await pagePromptTemplateVersionApi({
      currentPage: page,
      pageSize: pageSize.value,
      promptTemplateNo: promptId.value,
    });
    const list = Array.isArray(result) ? result : result.data || [];
    versions.value = list;
    total.value = result.totalCount;

    // ✅ 记住第一页的第一项（当前版本）
    if (page === 1 && list.length > 0 && !firstVersion.value) {
      firstVersion.value = list[0];
      loadPromptInfo(list[0]);
    }
  } catch (error) {
    console.error('获取版本历史失败:', error);
  } finally {
    loading.value = false;
  }
};

// 选择版本
const selectVersion = (version: PromptVersion) => {
  if (rollbacking.value || loading.value) return; // 回滚中禁止切换
  // selectedVersion.value = version;
  loadPromptInfo(version);
};
const rollbacking = ref(false);

// 回滚版本
const rollbackVersion = async (version: PromptVersion) => {
  try {
    await ElMessageBox.confirm(
      `确定要回滚到版本 ${version.dataVersion} 吗？`,
      '确认回滚',
      {
        type: 'warning',
      },
    );
    rollbacking.value = true;
    try {
      await rollBackPromptTemplateVersionApi({
        dataVersion: version?.dataVersion || 0,
        promptTemplateNo: version?.promptTemplateNo || '',
      });
      ElMessage.success('版本回滚成功');
      // 重置 firstVersion，让它能够重新设置为最新的第一项
      firstVersion.value = null;
      // 重新加载版本列表
      await loadVersions();
    } catch (error) {
      console.error('回滚失败:', error);
    } finally {
      rollbacking.value = false;
    }
  } catch {
    // 用户取消回滚
  }
};

// 返回列表
const goBack = () => {
  router.push('/prompt/list');
};

// 页面初始化
onMounted(() => {
  loadVersions();
});

const getRowClass = ({ row }: { row: PromptVersion }) => {
  return selectedVersion.value?.dataVersion === row.dataVersion
    ? 'is-selected'
    : '';
};

const handlePageChange = (page: number) => {
  currentPage.value = page;
  loadVersions(page);
};

const handleSizeChange = (val: number) => {
  pageSize.value = val;
  loadVersions();
};
</script>

<template>
  <Page
    :description="`查看和管理 ${promptName} 的版本历史，支持版本对比和回滚操作`"
    :title="`版本历史 - ${promptName}`"
  >
    <template #extra>
      <ElButton @click="goBack">
        <MdiArrowLeft class="mr-1 size-4" />
        返回列表
      </ElButton>
    </template>

    <ElRow :gutter="24">
      <!-- 左侧版本列表 -->
      <ElCol :span="12">
        <ElCard>
          <template #header>
            <div class="flex items-center">
              <MdiHistory class="mr-2 size-5 text-blue-500" />
              <span class="font-medium">版本列表</span>
            </div>
          </template>

          <div v-loading="loading">
            <ElTable
              :data="versions"
              highlight-current-row
              @current-change="selectVersion"
              :row-class-name="getRowClass"
              style="width: 100%"
              height="500px"
              :loading="rollbacking || loading"
            >
              <ElTableColumn label="版本号" prop="dataVersion" width="120">
                <template #default="{ row }">
                  <span class="font-semibold text-gray-800">
                    {{ ` 版本${row.dataVersion}` }}
                  </span>
                  <ElTag
                    v-if="row.dataVersion === firstVersion?.dataVersion"
                    size="small"
                    type="success"
                    class="ml-1"
                  >
                    当前
                  </ElTag>
                </template>
              </ElTableColumn>

              <ElTableColumn label="创建时间">
                <template #default="{ row }">
                  {{ formatDateTime(row.createdTime || '') }}
                </template>
              </ElTableColumn>

              <ElTableColumn label="创建人">
                <template #default="{ row }">
                  {{ row.createEmpDepartmentName ?? '--' }}-{{
                    row.createEmpName ?? '--'
                  }}
                </template>
              </ElTableColumn>

              <ElTableColumn label="操作" width="80">
                <template #default="{ row }">
                  <ElButton
                    v-if="row.dataVersion !== firstVersion?.dataVersion"
                    size="small"
                    type="success"
                    @click.stop="rollbackVersion(row)"
                    :disabled="row.dataVersion === firstVersion?.dataVersion"
                  >
                    回滚
                  </ElButton>
                </template>
              </ElTableColumn>
            </ElTable>

            <!-- 分页器 -->
            <div class="mt-4 flex justify-end">
              <ElPagination
                layout="sizes,prev, pager, next"
                :total="total"
                :page-size="pageSize"
                :current-page="currentPage"
                @current-change="handlePageChange"
                @size-change="handleSizeChange"
                :page-sizes="[10, 20, 50, 100]"
                background
                size="small"
              />
            </div>
          </div>
        </ElCard>
      </ElCol>

      <!-- 右侧版本内容 -->
      <ElCol :span="12">
        <ElCard>
          <template #header>
            <div class="flex items-center">
              <MdiTasks class="mr-2 size-5 text-green-500" />
              <span class="font-medium">版本内容</span>
              <span v-if="selectedVersion" class="ml-2 text-gray-500">
                - 版本 {{ selectedVersion.dataVersion }}
              </span>
            </div>
          </template>

          <div v-if="selectedVersion" class="space-y-6">
            <!-- 系统角色设定 -->
            <div class="rounded-lg border-l-4 border-blue-500 bg-blue-50 p-4">
              <div class="mb-3 flex items-center">
                <MdiUserMd class="mr-2 size-5 text-blue-600" />
                <h6 class="text-lg font-medium text-blue-900">系统角色设定</h6>
              </div>
              <div class="whitespace-pre-wrap text-gray-700">
                {{ selectedVersion.systemPrompt || '无' }}
              </div>
            </div>

            <!-- 任务模板 -->
            <div class="rounded-lg border-l-4 border-green-500 bg-green-50 p-4">
              <div class="mb-3 flex items-center">
                <MdiTasks class="mr-2 size-5 text-green-600" />
                <h6 class="text-lg font-medium text-green-900">任务模板</h6>
              </div>
              <div class="whitespace-pre-wrap text-gray-700">
                {{ selectedVersion.userPrompt || '无' }}
              </div>
            </div>

            <!-- 动态参数 -->
            <div
              class="rounded-lg border-l-4 border-purple-500 bg-purple-50 p-4"
            >
              <div class="mb-3 flex items-center">
                <MdiTags class="mr-2 size-5 text-purple-600" />
                <h6 class="text-lg font-medium text-purple-900">动态参数</h6>
              </div>
              <div class="flex flex-wrap gap-2">
                <ElTag
                  v-for="variable in selectedVersion.variables"
                  :key="variable"
                  class="text-sm"
                  size="small"
                  type="success"
                >
                  &#123;&#123;{{ variable }}&#125;&#125;
                </ElTag>
                <span
                  v-if="selectedVersion?.variables?.length === 0"
                  class="text-gray-500"
                >
                  无参数
                </span>
              </div>
            </div>
          </div>

          <div
            v-else
            class="flex h-64 items-center justify-center text-gray-500"
          >
            <div class="text-center">
              <MdiHistory class="mx-auto mb-3 size-12 text-gray-300" />
              <p>请点击左侧版本查看内容</p>
            </div>
          </div>
        </ElCard>
      </ElCol>
    </ElRow>
  </Page>
</template>

<style scoped>
/* 版本项悬停效果 */
.version-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* 当前版本特殊样式 */
.current-version {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border-color: #0ea5e9;
}

/* 内容区域样式 */
.content-section {
  transition: all 0.3s ease;
}

.content-section:hover {
  transform: translateY(-1px);
}
</style>
