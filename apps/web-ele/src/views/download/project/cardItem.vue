<script setup lang="ts">
import { computed, ref } from 'vue';
import { useRouter } from 'vue-router';

import { ElMessage, ElMessageBox } from 'element-plus';

import { deleteProject } from '../api';
import createProjectModel from './create-project-model.vue';

const props = defineProps({
  name: {
    type: String,
    default: () => '',
  },
  dec: {
    type: String,
    default: () => '',
  },
  createTime: {
    type: String,
    default: () => '',
  },
  manager: {
    type: Array,
    default: () => [],
  },
  senceNum: {
    type: Number,
    default: () => 0,
  },
  projectNo: {
    type: String,
    default: () => '',
  },
  deletable: {
    type: Boolean,
    default: () => false,
  },
});
const emits = defineEmits(['refreshList']);
const service = {
  deleteProject,
};
const projectModel = ref();
const router = useRouter();
const displayManager = computed(() => {
  const arr = props.manager.map((item: any) => item.empName);
  return arr.length > 0 && arr.length > 2
    ? {
        disabled: false,
        value: `${arr.slice(0, 2).join(', ')}, ......`,
      }
    : {
        disabled: true,
        value: arr.join(','),
      };
});
const toEdit = () => {
  projectModel.value.editProject();
};
const toDel = () => {
  ElMessageBox.confirm('是否确定删除?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(() => {
      service.deleteProject({ projectNo: props.projectNo }).then((res) => {
        ElMessage({
          type: 'success',
          message: res.data.msg,
        });
        emits('refreshList');
      });
    })
    .catch(() => {
      ElMessage({
        type: 'info',
        message: '已取消',
      });
    });
};
const enterProject = () => {
  router.push(`/download/scene?projectNo=${props.projectNo}`);
};
</script>

<template>
  <ElCard class="download-card">
    <div class="download-card-box">
      <div style="display: flex; justify-content: space-between">
        <span style="font-weight: bold">{{ props.name }}</span>
        <div style="display: flex; justify-content: space-between">
          <el-button type="primary" link size="small">
            <span
              class="icon-[mdi--square-edit-outline] size-5"
              @click="toEdit"
            ></span>
          </el-button>
          <el-button type="primary" link size="small">
            <span
              class="icon-[mdi--delete-forever-outline] size-5"
              @click="toDel"
              v-if="props.deletable"
            ></span>
          </el-button>
        </div>
      </div>
      <div>
        <span>描述:&nbsp;&nbsp;{{ props.dec }}</span>
      </div>
      <div>
        <span>创建时间:&nbsp;&nbsp;{{ props.createTime }}</span>
      </div>
      <div>
        <span>场景数:&nbsp;&nbsp;{{ props.senceNum }}</span>
      </div>
      <div>
        <span>管理员:&nbsp;&nbsp;</span>
        <el-tooltip
          class="box-item"
          effect="dark"
          :content="props.manager.map((item: any) => item.empName).join(',')"
          placement="top"
          :disabled="displayManager.disabled"
        >
          <span>{{ displayManager.value }}</span>
        </el-tooltip>
      </div>
      <div style="display: flex; justify-content: flex-end; width: 100%">
        <el-button @click="enterProject" type="primary" link>
          进入项目
        </el-button>
      </div>
    </div>
    <createProjectModel
      ref="projectModel"
      @refresh-list="() => emits('refreshList')"
      :project-name="props.name"
      :administrator-emp-code-list="
        props.manager.map((item: any) => item.empCode)
      "
      :project-desc="props.dec"
      :project-no="props.projectNo"
      :default-option="props.manager"
    />
  </ElCard>
</template>

<style lang="scss" scoped>
.download-card {
  margin-top: 20px;
  margin-right: 20px;

  .download-card-box {
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    width: 340px;
    height: 150px;
  }
}
</style>
