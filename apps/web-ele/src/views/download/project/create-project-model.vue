<script lang="ts" setup>
import { ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { useVbenForm } from '#/adapter/form';

import { createProject, editProjectApi, listEmpByQuery } from '../api';

const props = defineProps({
  projectName: {
    type: String,
    default: () => '',
  },
  administratorEmpCodeList: {
    type: Array,
    default: () => [],
  },
  projectDesc: {
    type: String,
    default: () => '',
  },
  projectNo: {
    type: String,
    default: () => '',
  },
  defaultOption: {
    type: Array as any,
    default: () => [],
  },
});
const emits = defineEmits(['refreshList']);
const service = {
  createProject,
  listEmpByQuery,
  editProjectApi,
};
const name = ref('新建项目');

const [Modal, modalApi] = useVbenModal({
  footer: false,
});
const options = ref<any>([]);
const getEmpList = (query: null | string) => {
  service
    .listEmpByQuery({
      currentPage: 1,
      pageSize: 50,
      matchKey: query,
    })
    .then((res) => {
      selectLoading.value = false;
      options.value = res.data.map((item: any) => {
        return {
          ...item,
          empName: `${item.empName}(${item.empCode})`,
        };
      });
    });
};
const open = () => {
  name.value = '新建项目';
  modalApi.open();
};
const selectLoading = ref(false);
const remoteMethod = (query: string) => {
  if (query) {
    selectLoading.value = true;
    getEmpList(query);
  } else {
    options.value = [];
  }
};
const [Form, formApi] = useVbenForm({
  // 所有表单项共用，可单独在表单内覆盖
  commonConfig: {
    // 所有表单项
    componentProps: {
      class: 'w-full',
    },
  },
  resetButtonOptions: {
    content: '取消',
  },
  // 提交函数
  handleSubmit: onSubmit,
  handleReset: onResetForm,
  // 垂直布局，label和input在不同行，值为vertical
  // 水平布局，label和input在同一行
  layout: 'horizontal',
  schema: [
    {
      // 组件需要在 #/adapter.ts内注册，并加上类型
      component: 'Input',
      // 对应组件的参数
      componentProps: {
        placeholder: '请输入',
      },
      // 字段名
      fieldName: 'projectName',
      // 界面显示的label
      label: '项目名称',
      rules: 'required',
    },
    {
      component: 'Select',
      fieldName: 'administratorEmpCodeList',
      label: '项目管理员',
      rules: 'required',
    },
    {
      component: 'Input',
      componentProps: {
        allowClear: true,
        showWordlimit: true,
        maxlength: '50',
        type: 'textarea',
        placeholder: '请输入不超过50字',
        showSearch: true,
      },
      defaultValue: '',
      fieldName: 'projectDesc',
      label: '项目描述',
    },
  ],
  wrapperClass: 'grid-cols-1',
});
function onSubmit(values: any) {
  if (name.value === '编辑项目') {
    service
      .editProjectApi({ ...values, projectNo: props.projectNo })
      .then(() => {
        modalApi.close();
        emits('refreshList');
      });
  } else {
    service.createProject(values).then(() => {
      modalApi.close();
      emits('refreshList');
    });
  }
}

function onResetForm() {
  modalApi.close();
}

const editProject = () => {
  formApi.setValues({
    projectName: props.projectName || '',
    administratorEmpCodeList:
      props.administratorEmpCodeList.length > 0
        ? props.administratorEmpCodeList
        : null,
    projectDesc: props.projectDesc || '',
  });
  modalApi.open();
  name.value = '编辑项目';
};

defineExpose({
  open,
  editProject,
});
</script>

<template>
  <Modal class="w-[600px]" :title="name">
    <Form>
      <template #administratorEmpCodeList="slotProps">
        <ElSelect
          multiple
          filterable
          remote
          reserve-keyword
          clearable
          :remote-method="remoteMethod"
          placeholder="请选择"
          v-bind="slotProps"
          :automatic-dropdown="true"
          remote-show-suffix
        >
          <el-option
            v-for="item in props.defaultOption"
            :key="item.empCode"
            :label="item.empName"
            :value="item.empCode"
          />
          <el-option
            v-for="item in options"
            :key="item.empCode"
            :label="item.empName"
            :value="item.empCode"
          />
        </ElSelect>
      </template>
    </Form>
  </Modal>
</template>
