<script lang="ts" setup>
import { ref } from 'vue';

import { projectList } from '../api';
import CardItem from './cardItem.vue';
import createProjectModel from './create-project-model.vue';

const service = {
  projectList,
};

const input = ref('');

interface CardConfigItem {
  administratorList: Array<string>; // 管理员列表
  createTime: string;
  deletable: boolean;
  memberList: Array<string>;
  projectDesc: string;
  projectName: string;
  projectNo: string;
  sceneCount: number;
}

const loading = ref(false);

const cardConfig = ref<CardConfigItem[]>([]);

const getList = () => {
  loading.value = true;
  service
    .projectList({ keyword: input.value })
    .then((res: any) => {
      loading.value = false;
      cardConfig.value = res.map((item: any) => {
        return {
          ...item,
          administratorList: item.administratorList.map((item2: any) => {
            return {
              ...item2,
              empName: `${item2.empName}(${item2.empCode})`,
            };
          }),
        };
      });
    })
    .finally(() => {
      loading.value = false;
    });
};
getList();
const projectModel = ref();
const createProject = () => {
  projectModel.value.open();
};
const refreshList = () => {
  input.value = '';
  getList();
};
</script>

<template>
  <ElCard class="port-list vp-raw w-full" v-loading="loading">
    <el-input
      v-model="input"
      style="width: 240px"
      placeholder="搜索项目"
      :clearable="true"
      @change="!input && getList()"
    />
    <el-button @click="getList()" style="margin-left: 10px" type="primary">
      搜索
    </el-button>
    <el-button
      @click="createProject()"
      style="margin-left: 10px"
      type="primary"
    >
      +新建项目
    </el-button>
    <div class="download-card-space">
      <CardItem
        v-for="(item, index) in cardConfig"
        :key="index"
        :name="item.projectName"
        :dec="item.projectDesc"
        :create-time="item.createTime"
        :manager="item.administratorList"
        :sence-num="item.sceneCount"
        :project-no="item.projectNo"
        :deletable="item.deletable"
        @refresh-list="refreshList"
      />
    </div>
    <createProjectModel ref="projectModel" @refresh-list="refreshList" />
  </ElCard>
</template>

<style lang="scss" scoped>
.download-card-space {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
}
</style>
