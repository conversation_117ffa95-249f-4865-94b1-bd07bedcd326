import { requestClient } from '#/api/request';

export const merApiURL = import.meta.env.VITE_MER_API_URL;

interface ProjectList {
  keyword: string;
}
interface CreateProject {
  administratorEmpCodeList: string[];
  memberEmpCodeList: string[];
  projectDesc: string;
  projectName: string;
}
// 项目列表
export function projectList(data: ProjectList) {
  return requestClient.post('/hera/yxt-export/b/exportProject/r/1.0/list', {
    ...data,
  });
}
// 创建下载项目
export function createProject(data: CreateProject) {
  return requestClient.post('/hera/yxt-export/b/exportProject/w/1.0/create', {
    ...data,
  });
}
// 编辑下载项目
export function editProjectApi(data: CreateProject) {
  return requestClient.post('/hera/yxt-export/b/exportProject/w/1.0/edit', {
    ...data,
  });
}
// 根据条件查询员工信息
export function listEmpByQuery(data: any) {
  return requestClient.post(
    '/hera/yxt-export/b/exportProject/r/1.0/pageQueryEmp',
    {
      ...data,
    },
  );
}
// 删除项目
export function deleteProject(data: any) {
  return requestClient.post(
    '/hera/yxt-export/b/exportProject/w/1.0/delete',
    {
      ...data,
    },
    {
      responseReturn: 'raw',
    },
  );
}

// 获取场景
export function getScenePageList(data: any) {
  return requestClient.post('/hera/yxt-export/b/exportScene/r/1.0/pageList', {
    ...data,
  });
}

// 创建场景
export function createScene(data: any) {
  return requestClient.post(
    '/hera/yxt-export/b/exportScene/w/1.0/create',
    {
      ...data,
    },
    {
      responseReturn: 'raw',
    },
  );
}

// 启用/停用场景
export function changeStatus(data: any) {
  return requestClient.post(
    '/hera/yxt-export/b/exportScene/w/1.0/changeStatus',
    {
      ...data,
    },
  );
}

// 删除场景
export function deleteScene(data: any) {
  return requestClient.post('/hera/yxt-export/b/exportScene/w/1.0/delete', {
    ...data,
  });
}

// 获取
export function getScene(data: any) {
  return requestClient.post('/hera/yxt-export/b/exportScene/r/1.0/get', {
    ...data,
  });
}
// 编辑场景
export function exportScene(data: CreateProject) {
  return requestClient.post(
    '/hera/yxt-export/b/exportScene/w/1.0/edit',
    {
      ...data,
    },
    {
      responseReturn: 'raw',
    },
  );
}
