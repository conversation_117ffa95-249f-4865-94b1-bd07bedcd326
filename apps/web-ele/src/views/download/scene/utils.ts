import axios from 'axios';

import { requestClient } from '#/api/request';
// 根据枚举名称查询枚举集合信息
export async function getPostSignatureByDict() {
  return requestClient.post(
    '/hera/yxt-export/b/upload/file/r/1.0/getPostSignatureByDict',
    {},
    {
      headers: {
        bizReferer: `/download/scene`,
      },
    },
  );
}

// /** 上传文件至OBS */
// export async function uploadFileToObsApi(baseUrl: string, data: any) {
//   return requestClient.post(
//     `https://${baseUrl}`,
//       data,
//     {
//       headers: { 'Content-Type': 'multipart/form-data' },
//       withCredentials: false,
//     },
//   );
// }

export const uploadFileToObs1 = (
  fileOrBlob: any,
  key: string,
): Promise<any> => {
  return new Promise((resolve, reject) => {
    let base: string;
    let fileKey: any;
    let formData;

    // 1，获取华为云信息，调获取上传信息的接口
    getPostSignatureByDict()
      .then((res) => {
        const hwObsUploadInfo = res;
        base = hwObsUploadInfo.base;
        // 2，获取文件
        formData = getObsUploadParams(fileOrBlob, hwObsUploadInfo, key);
        fileKey = formData.get('key');

        // 3，调取上传接口
        return axios({
          url: `https://${base}`,
          baseURL: '',
          data: formData,
          headers: {
            'Content-Type': 'multipart/form-data',
          },
          method: 'post',
        }).then(() => {
          // 返回参数传回给后端
          resolve({
            fileKey,
          });
        });
      })
      .catch((error) => {
        // 处理错误
        reject(error);
      });
  });
};
const getObsUploadParams = (
  fileOrBlob: any,
  hwObsUploadInfo: any,
  key: string,
) => {
  const _prototype = (obj: any) => Object.prototype.toString.call(obj);
  const _isFile = (val: any) => _prototype(val) === '[object File]';
  const _isObject = (val: any) => _prototype(val) === '[object Object]';
  const _isFunction = (val: any) => _prototype(val) === '[object Function]';
  const _isBlob = (val: any) => _isObject(val) && _isFunction(val.blob);
  // 获取文件名 、文件
  const isFile = _isFile(fileOrBlob);
  const isBlob = _isBlob(fileOrBlob);
  if (!isFile && !isBlob) {
    throw new Error('参数类型只能是blob、File');
  }
  const fileName = isBlob ? fileOrBlob.filename() : fileOrBlob.name;
  const file = isBlob ? fileOrBlob.blob() : fileOrBlob;
  // obs 上传信息
  const { prefix, accesskeyid, signature, policy, acl } = hwObsUploadInfo;
  if (key) {
    // key = `${prefix}/${key}`;
  } else {
    // 不能让名字重复这里用时间戳加随机3位整数的方法
    key = `${prefix}/${Math.floor(Math.random() * 900) + 100}${Date.now()}${fileName}`;
  }
  const params: any = {
    accesskeyid,
    signature,
    policy,
    key,
    'x-obs-acl': acl,
    // 文件参数务必放在最后，否则会有问题
    file,
  };
  const formData = new FormData();
  for (const key in params) formData.append(key, params[key]);
  return formData;
};
