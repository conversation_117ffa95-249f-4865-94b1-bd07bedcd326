<script setup lang="ts">
import { computed } from 'vue';

import { ElRadio, ElRadioGroup } from 'element-plus';

const props = defineProps({
  modelValue: {
    type: [String, Number],
    default: '',
  },
  options: {
    type: Array as () => { label: string; value: number | string }[],
    default: () => [],
    required: true,
  },
  color: {
    type: String,
    default: '#409eff',
  },
});

const emit = defineEmits(['update:modelValue', 'change', 'clearValue']);

const selectedValue = computed({
  get: () => props.modelValue,
  set: (newValue) => {
    emit('update:modelValue', newValue);
  },
});

const handleRadioChange = (value: number | string) => {
  emit('change', value);
  emit('clearValue');
};
</script>

<template>
  <div>
    <ElRadioGroup v-model="selectedValue" @change="handleRadioChange">
      <ElRadio
        v-for="option in options"
        :key="option.value"
        :value="option.value"
      >
        {{ option.label }}
        <slot
          v-if="option.value === selectedValue"
          :name="`radioSuffix_${option.value}`"
        ></slot>
      </ElRadio>
    </ElRadioGroup>
  </div>
</template>

<style scoped></style>
