<script setup lang="ts">
import { ref } from 'vue';

import { ElMessage } from 'element-plus';

import { uploadFileToObs1 } from '../utils';
import upFile from './up-file.vue';

const props = defineProps({
  layout: {
    type: String,
    default: () => '',
  },
});
// const openUp = ()=>{
//   upFileRef?.value?.open()
// }
const emits = defineEmits(['listChange']);
const upFileRef = ref();
const fileData = ref<any>([]);

const delFile = (index: number) => {
  fileData.value.splice(index, 1);
};

const upSuccess = (val: any) => {
  fileData.value.push(val);
  emits('listChange', fileData.value);
};
const setList = (list: Array<any>) => {
  fileData.value = list;
};
const beforeUpload = async (file: any, layout: String, index: number) => {
  const extension = file.name
    .slice(Math.max(0, file.name.lastIndexOf('.') + 1))
    .toUpperCase();
  if (extension !== layout) {
    ElMessage.error(`请上传${layout}格式的文件`);
    return false;
  }
  uploadFileToObs1(file, '').then((res) => {
    ElMessage.success('上传成功');
    fileData.value[index].fileKey = res.fileKey;
  });
  return false;
};
defineExpose({
  setList,
});
</script>

<template>
  <div>
    <upFile ref="upFileRef" @up-success="upSuccess" :layout="props.layout" />
    <TransitionGroup name="fade">
      <div
        v-for="(item, index) in fileData"
        :key="index"
        style="display: flex; align-items: center; justify-content: flex-start"
      >
        <div class="file-item">
          <el-tooltip
            class="box-item"
            effect="dark"
            :content="`模板OBS key: ${item.fileKey}<br/>模板编号: ${item.templateNo || '-'}`"
            placement="top-start"
            :raw-content="true"
          >
            <span class="overflow">
              {{ item.fileKey }}
            </span>
          </el-tooltip>
          <span>
            <el-button type="primary" link size="small" @click="delFile(index)">
              <span class="icon-[mdi--delete-forever-outline] size-5"></span>
            </el-button>
          </span>
        </div>
        <el-upload
          class="upload-demo"
          action="https://run.mocky.io/v3/9d059bf9-4660-45f2-925d-ce80ad6c4d15"
          :limit="1"
          :auto-upload="true"
          :before-upload="
            (file: object) => {
              return beforeUpload(file, props.layout, index);
            }
          "
        >
          <template #trigger>
            <el-button
              style="position: relative; top: 5px"
              type="primary"
              link
              size="small"
            >
              <span class="icon-[mdi--square-edit-outline] size-5"></span>
            </el-button>
          </template>
        </el-upload>
      </div>
    </TransitionGroup>
  </div>
</template>

<style scoped lang="scss">
.file-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 400px;
  padding: 3px 3px 3px 5px;
  margin: 6px 0;
  font-size: 14px;
}

.overflow {
  display: inline-block;
  max-width: 400px; /* 设置最大宽度 */
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 添加过渡动画样式 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.5s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
