<script lang="ts" setup>
import { computed } from 'vue';

import { useVbenModal } from '@vben/common-ui';

const props = defineProps({
  modelValue: {
    type: String,
    default: () => '',
    required: true,
  },
});
const emit = defineEmits(['update:modelValue', 'change']);

const [Modal, modalApi] = useVbenModal({
  onConfirm: () => {
    modalApi.close();
  },
  onCancel: () => {
    modalApi.close();
    emit('update:modelValue', '');
  },
});

const words = computed({
  get: () => props.modelValue,
  set: (newValue: string) => {
    emit('update:modelValue', newValue);
  },
});

const open = () => {
  modalApi.open();
};

defineExpose({
  open,
});
</script>
<template>
  <div>
    <Modal class="w-[600px]" title="字段映射规则编辑">
      <ElInput
        type="textarea"
        placeholder="提供代码输入"
        v-model="words"
        :autosize="{ minRows: 6 }"
      />
    </Modal>
  </div>
</template>
