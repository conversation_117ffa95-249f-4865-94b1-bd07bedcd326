<script setup lang="ts">
import { ref } from 'vue';

import { ElMessage } from 'element-plus';

import { uploadFileToObs1 } from '../utils';

const props = defineProps({
  layout: {
    type: String,
    default: () => '',
  },
});
const emits = defineEmits(['upSuccess']);

const uploadRef = ref();

const beforeUpload = async (file: any, layout: String) => {
  const extension = file.name
    .slice(Math.max(0, file.name.lastIndexOf('.') + 1))
    .toUpperCase();
  if (extension !== layout) {
    ElMessage.error(`请上传${layout}格式的文件`);
    return false;
  }
  uploadFileToObs1(file, '').then((res) => {
    ElMessage.success('上传成功');
    emits('upSuccess', res);
  });
  return false;
};
</script>

<template>
  <el-upload
    ref="uploadRef"
    class="upload-demo"
    action=""
    :limit="1"
    :auto-upload="true"
    :before-upload="
      (file: object) => {
        return beforeUpload(file, props.layout);
      }
    "
  >
    <template #trigger>
      <el-button size="small" type="primary">上传文件模板</el-button>
    </template>
    <template #tip>
      <div class="el-upload__tip text-red">仅支持上传{{ props.layout }}</div>
    </template>
  </el-upload>
</template>
