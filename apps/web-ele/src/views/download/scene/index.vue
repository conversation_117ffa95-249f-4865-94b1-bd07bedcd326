<script lang="ts" setup>
import type { VbenFormProps } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';

import { computed, nextTick, ref } from 'vue';
import { useRoute } from 'vue-router';

import { ElCard, ElMessage, ElMessageBox } from 'element-plus';

import { useVbenVxeGrid } from '#/adapter/vxe-table';

import { changeStatus, deleteScene, getScenePageList } from '../api';
import createSceneModel from './create-scene-model.vue';

const route = useRoute();
const projectNo = computed(() => {
  return route.query.projectNo;
});
const createSceneModelRef = ref();
const formOptions: VbenFormProps = {
  // 默认展开
  collapsed: false,
  showCollapseButton: false,
  schema: [
    {
      component: 'Input',
      componentProps: {
        clearable: true,
        placeholder: '请输入',
      },
      fieldName: 'keyword',
      label: '搜索场景',
    },
  ],
  submitButtonOptions: {
    content: '查询',
  },
  // 是否在字段值改变时提交表单
  submitOnChange: false,
  // 按下回车时是否提交表单
  submitOnEnter: false,
};

const gridOptions: VxeGridProps = {
  minHeight: 350,
  align: 'left',
  columns: [
    {
      field: 'sceneNo',
      title: '场景编码',
      width: 300,
    },
    { field: 'sceneName', title: '场景名称' },
    { field: 'sceneDesc', title: '场景描述' },
    { field: 'empName', title: '最后编辑人' },
    { field: 'updatedTime', title: '最后编辑时间' },
    {
      field: 'status',
      title: '状态',
      slots: {
        default: ({ row }) => {
          if (row.status === 'ENABLE') {
            return '启用';
          } else if (row.status === 'DISABLE') {
            return '停用';
          } else {
            return '-';
          }
        },
      },
    },
    {
      title: '操作',
      width: 300,
      slots: {
        default: 'action',
      },
    },
  ],
  keepSource: true,
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        const res = await getScenePageList({
          ...formValues,
          currentPage: page.currentPage,
          pageSize: page.pageSize,
          projectNo: projectNo.value,
        });
        const obj = {
          total: res.totalCount,
          items: res.data,
        };
        return obj;
      },
    },
  },
  toolbarConfig: {
    // 是否显示搜索表单控制按钮
    // @ts-ignore 正式环境时有完整的类型声明
    // search: true,
  },
};

const [Grid, gridApi] = useVbenVxeGrid({ formOptions, gridOptions });
const handleAction = (type: string, row: any) => {
  let message: string;
  let title: string;
  let apiCall: Function;
  const params = {
    projectNo: row.projectNo,
    sceneNo: row.sceneNo,
  };
  switch (type) {
    case 'delete': {
      message = '确定要删除这条记录吗？删除后数据将无法恢复！';
      title = '删除确认';
      apiCall = () => deleteScene(params);
      break;
    }
    case 'disable': {
      message = '确定要停用这条记录吗？';
      title = '停用确认';
      apiCall = () => changeStatus({ ...params, status: 'DISABLE' });
      break;
    }
    case 'edit': {
      message = '确定要编辑这条记录吗？';
      title = '编辑确认';
      handleEdit(row);
      // apiCall = () => console.log('编辑接口调用', row); // 替换为实际的编辑接口调用
      return;
    }
    case 'enable': {
      message = '确定要启用这条记录吗？';
      title = '启用确认';
      apiCall = () => changeStatus({ ...params, status: 'ENABLE' });
      break;
    }
    default: {
      return;
    }
  }
  ElMessageBox.confirm(message, title, {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(() => {
      apiCall().then(() => {
        ElMessage.success(`操作成功`);
        gridApi.reload(); // 刷新表格数据
      });
    })
    .catch(() => {
      ElMessage.info('已取消操作');
    });
};

const handleCreate = () => {
  nextTick(() => {
    createSceneModelRef.value?.open();
  });
};
const handleEdit = (row: any) => {
  nextTick(() => {
    createSceneModelRef.value?.open(row, 'edit');
  });
};
const refresh = () => {
  gridApi.reload();
};
</script>

<template>
  <ElCard class="vp-raw w-full">
    <Grid>
      <template #toolbar>
        <ElButton class="mb-2" type="primary" @click="handleCreate">
          新建场景
        </ElButton>
      </template>
      <template #action="{ row }">
        <ElButton
          size="small"
          type="primary"
          @click="handleAction('edit', row)"
        >
          编辑
        </ElButton>
        <ElButton
          size="small"
          type="success"
          @click="handleAction('enable', row)"
          v-show="row.status === 'DISABLE'"
        >
          启用
        </ElButton>
        <ElButton
          size="small"
          type="warning"
          @click="handleAction('disable', row)"
          v-show="row.status !== 'DISABLE'"
        >
          停用
        </ElButton>
        <ElButton
          size="small"
          type="danger"
          @click="handleAction('delete', row)"
          v-show="row.status === 'DISABLE'"
        >
          删除
        </ElButton>
      </template>
    </Grid>
    <createSceneModel ref="createSceneModelRef" @refresh="refresh" />
  </ElCard>
</template>
