<script lang="ts" setup>
import { computed, nextTick, reactive, ref } from 'vue';
import { useRoute } from 'vue-router';

import { useVbenModal } from '@vben/common-ui';

import { ElMessage } from 'element-plus';

import { useVbenForm } from '#/adapter/form';

import { createScene as createSceneApi, exportScene, getScene } from '../api';
import fileModel from './components/file-model.vue';
import RadioWithSlot from './components/radio-with-slot.vue';
import {
  approvalOption,
  fileNameMapOptions,
  fileTypeOption,
  passportRuleOption,
  pushWxOption,
  watermarkRuleOption,
  wordMapOptions,
} from './config.js';

const emits = defineEmits(['refresh']);
const modalName = ref('新建场景');

const [Modal, modalApi] = useVbenModal({
  footer: false,
  onClosed: () => ModalClose(),
});

const otherForm = reactive({
  fileName: '',
  flowKey: '',
  frequencyLimit: {
    hour: null,
    time: null,
  },
  exportTemplateList: [],
});
const ModalClose = () => {
  otherForm.frequencyLimit.hour = null;
  otherForm.frequencyLimit.time = null;
};
const formatValue = ref('XLSX');
const formChange = (value: any) => {
  nextTick(() => {
    formatValue.value = value.fileType === 'EXCEL' ? 'XLSX' : value.fileType;
  });
};
const [Form, formApi] = useVbenForm({
  // 所有表单项共用，可单独在表单内覆盖
  commonConfig: {
    // 所有表单项
    componentProps: {
      class: 'w-full',
    },
  },
  resetButtonOptions: {
    content: '取消',
  },
  // 提交函数
  handleSubmit: onSubmit,
  handleReset: onResetForm,
  handleValuesChange: formChange,
  schema: [
    {
      // 组件需要在 #/adapter.ts内注册，并加上类型
      component: 'Input',
      // 对应组件的参数
      componentProps: {
        placeholder: '请输入场景编码',
      },
      // 字段名
      fieldName: 'sceneNo',
      // 界面显示的label
      label: '场景编码',
      rules: 'required',
    },
    {
      // 组件需要在 #/adapter.ts内注册，并加上类型
      component: 'Input',
      // 对应组件的参数
      componentProps: {
        placeholder: '请输入场景名称',
      },
      // 字段名
      fieldName: 'sceneName',
      // 界面显示的label
      label: '场景名称',
      rules: 'required',
    },
    {
      component: 'Input',
      componentProps: {
        allowClear: true,
        type: 'textarea',
        placeholder: '(选填)',
        showSearch: true,
      },
      defaultValue: '',
      fieldName: 'sceneDesc',
      label: '场景描述',
    },
    {
      // 组件需要在 #/adapter.ts内注册，并加上类型
      component: 'Input',
      // 对应组件的参数
      componentProps: {
        placeholder: '请输入服务名称',
      },
      // 字段名
      fieldName: 'serviceName',
      // 界面显示的label
      label: '服务名称',
      rules: 'required',
    },
    {
      // 组件需要在 #/adapter.ts内注册，并加上类型
      component: 'Input',
      // 对应组件的参数
      componentProps: {
        placeholder: '请输入接口地址',
      },
      // 字段名
      fieldName: 'interfacePath',
      // 界面显示的label
      label: '接口地址',
      rules: 'required',
    },
    {
      // 组件需要在 #/adapter.ts内注册，并加上类型
      component: 'InputNumber',
      // 对应组件的参数
      componentProps: {
        placeholder: '请输入分页大小',
        controls: false,
        precision: 0,
        min: 0,
      },
      // 字段名
      fieldName: 'pageSize',
      // 界面显示的label
      label: '分页大小',
      rules: 'required',
    },
    // {
    //   // 组件需要在 #/adapter.ts内注册，并加上类型
    //   component: 'Input',
    //   // 对应组件的参数
    //   componentProps: {
    //     placeholder: '请输入',
    //   },
    //   // 字段名
    //   fieldName: 'yinse',
    //   // 界面显示的label
    //   label: '字段映射',
    // },
    {
      // 组件需要在 #/adapter.ts内注册，并加上类型
      component: 'Input',
      // 对应组件的参数
      componentProps: {
        placeholder: '请输入',
      },
      // 字段名
      fieldName: 'fileNameType',
      // 界面显示的label
      label: '文件名称',
      rules: 'required',
      defaultValue: 'RANDOM',
    },
    {
      component: 'RadioGroup',
      componentProps: {
        options: fileTypeOption,
      },
      // 字段名
      fieldName: 'fileType',
      // 界面显示的label
      label: '文件格式',
      rules: 'required',
      defaultValue: 'EXCEL',
    },
    {
      // 组件需要在 #/adapter.ts内注册，并加上类型
      component: 'Input',
      // 对应组件的参数
      componentProps: {
        placeholder: '请输入',
      },
      // 字段名
      fieldName: 'exportTemplateList',
      // 界面显示的label
      label: '文件模板',
      rules: 'required',
      defaultValue: 'null',
    },
    {
      // 组件需要在 #/adapter.ts内注册，并加上类型
      component: 'InputNumber',
      // 对应组件的参数
      componentProps: {
        placeholder: '请输入天数',
        controls: false,
        min: 0,
      },
      // 字段名
      fieldName: 'expireDays',
      // 界面显示的label
      label: '文件保留时长',
    },
    {
      component: 'RadioGroup',
      componentProps: {
        options: passportRuleOption,
      },
      // 字段名
      fieldName: 'passportRule',
      // 界面显示的label
      label: '文件加密',
      rules: 'required',
      defaultValue: 'EMPLOYEE_NO',
    },
    {
      component: 'RadioGroup',
      componentProps: {
        options: watermarkRuleOption,
      },
      // 字段名
      fieldName: 'watermarkRule',
      // 界面显示的label
      label: '文件水印',
      rules: 'required',
      defaultValue: 'NAME_EMPLOYEE_NO',
    },
    {
      // 组件需要在 #/adapter.ts内注册，并加上类型
      component: 'InputNumber',
      // 对应组件的参数
      componentProps: {
        placeholder: '请输入最大导出数量',
        controls: false,
        precision: 0,
        min: 0,
      },
      // 字段名
      fieldName: 'maxRow',
      // 界面显示的label
      label: '导出上限',
    },
    {
      // 组件需要在 #/adapter.ts内注册，并加上类型
      component: 'Input',
      // 对应组件的参数
      componentProps: {
        placeholder: '请输入天数',
      },
      // 字段名
      fieldName: 'frequencyLimit',
      // 界面显示的label
      label: '导出频次限制',
    },
    {
      component: 'RadioGroup',
      componentProps: {
        options: approvalOption,
      },
      // 字段名
      fieldName: 'approval',
      // 界面显示的label
      label: '审批流程',
      rules: 'required',
      defaultValue: 'DEFAULT',
    },
    {
      component: 'RadioGroup',
      componentProps: {
        options: pushWxOption,
      },
      // 字段名
      fieldName: 'pushWx',
      // 界面显示的label
      label: '企微推送',
      rules: 'required',
      defaultValue: true,
    },
  ],
  wrapperClass: 'grid-cols-1',
});
const route = useRoute();
const projectNo = computed(() => {
  return route.query.projectNo;
});
function onSubmit(values: any) {
  if (modalName.value === '编辑场景') {
    exportScene({
      ...values,
      ...otherForm,
      projectNo: projectNo.value,
      interfaceType: 'STANDARD',
    }).then((res) => {
      if (res.data.code === '10000') {
        ElMessage.success(res.data.msg);
        modalApi.close();
        emits('refresh');
      } else {
        ElMessage.error(res.data.msg);
      }
    });
    return;
  }
  createSceneApi({
    ...values,
    ...otherForm,
    projectNo: projectNo.value,
    interfaceType: 'STANDARD',
  }).then((res) => {
    if (res.data.code === '10000') {
      ElMessage.success(res.data.msg);
      modalApi.close();
      emits('refresh');
    } else {
      ElMessage.error(res.data.msg);
    }
  });
}
const listChange = (val: any) => {
  otherForm.exportTemplateList = val;
  // console.log('otherForm.exportTemplateList', otherForm.exportTemplateList)
};
function onResetForm() {
  modalApi.close();
}
const fileModelRef = ref();
const open = (row: any, type: String) => {
  if (type === 'edit') {
    modalName.value = '编辑场景';
    // 调取回显接口
    // console.log('row', row)
    getScene({
      projectNo: row.projectNo,
      sceneNo: row.sceneNo,
    }).then((data) => {
      otherForm.fileName = data.fileName;
      otherForm.flowKey = data.flowKey;
      otherForm.frequencyLimit.hour = data.frequencyLimit?.hour;
      otherForm.frequencyLimit.time = data.frequencyLimit?.time;
      otherForm.exportTemplateList = data.exportTemplateList;
      formApi.setValues(data);
      fileModelRef.value.setList(data.exportTemplateList || []);
    });
    formApi.updateSchema([
      {
        // 组件需要在 #/adapter.ts内注册，并加上类型
        component: 'Input',
        // 对应组件的参数
        componentProps: {
          placeholder: '请输入场景编码',
          disabled: true,
        },
        // 字段名
        fieldName: 'sceneNo',
        // 界面显示的label
        label: '场景编码',
        rules: 'required',
      },
    ]);
    modalApi.open();
  } else {
    modalName.value = '新建场景';
    formApi.updateSchema([
      {
        // 组件需要在 #/adapter.ts内注册，并加上类型
        component: 'Input',
        // 对应组件的参数
        componentProps: {
          placeholder: '请输入场景编码',
          disabled: false,
        },
        // 字段名
        fieldName: 'sceneNo',
        // 界面显示的label
        label: '场景编码',
        rules: 'required',
      },
    ]);
    modalApi.open();
  }
};

defineExpose({
  open,
});
</script>

<template>
  <div>
    <Modal class="w-[600px]" :title="modalName">
      <Form>
        <!-- <template #yinse="slotProps">
          <RadioWithSlot
            :options="wordMapOptions"
            v-bind="slotProps"
            @clear-value="() => (otherForm.rules = '')"
          >
            <template #radioSuffix_1>
              <el-tag @click="editRules" type="primary">
                {{ !otherForm.rules ? '写入规则' : '编辑规则' }}
              </el-tag>
            </template>
          </RadioWithSlot>
        </template> -->
        <template #fileNameType="slotProps">
          <RadioWithSlot
            :options="fileNameMapOptions"
            v-bind="slotProps"
            @clear-value="() => (otherForm.fileName = '')"
          >
            <template #radioSuffix_FIXED>
              <el-input
                place-holder="请输入"
                style="width: 150px"
                v-model="otherForm.fileName"
              />
            </template>
          </RadioWithSlot>
        </template>
        <template #exportTemplateList>
          <file-model
            ref="fileModelRef"
            :layout="formatValue"
            @list-change="listChange"
          />
        </template>
        <template #frequencyLimit>
          <el-input-number
            style="width: 110px"
            :controls="false"
            :precision="0"
            v-model="otherForm.frequencyLimit.hour"
            :min="1"
          />
          小时内最多
          <el-input-number
            style="width: 110px"
            :controls="false"
            :precision="0"
            v-model="otherForm.frequencyLimit.time"
            :min="1"
          />
          次
        </template>
        <template #approval="slotProps">
          <RadioWithSlot
            :options="wordMapOptions"
            v-bind="slotProps"
            @clear-value="() => (otherForm.flowKey = '')"
          >
            <template #radioSuffix_CUSTOM>
              <el-input
                place-holder="请输入"
                style="width: 150px"
                v-model="otherForm.flowKey"
              />
            </template>
          </RadioWithSlot>
        </template>
        <template #fileType="slotProps">
          <ElRadioGroup v-bind="slotProps">
            <ElRadio
              v-for="option in fileTypeOption"
              :key="option.value"
              :value="option.value"
              :disabled="option.value === 'PDF'"
            >
              {{ option.label }}
            </ElRadio>
          </ElRadioGroup>
        </template>
      </Form>
    </Modal>
    <!-- <EditRules v-model="otherForm.rules" ref="EditRulesRef" /> -->
  </div>
</template>
