<script lang="ts" setup>
import { ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { ElMessage } from 'element-plus';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import useAuthGateway from '#/views/port/list/useAuthGateway';

import { authInterfaceApi, getGridOptions, queryFormOptions } from './common';

const props = defineProps<{
  appKey: string;
}>();

const emit = defineEmits(['authSuccess']);

const [Modal, modalApi] = useVbenModal({
  onOpened: () => {},
});

const title = '添加授权接口';

const [QueryGrid, gridApi] = useVbenVxeGrid({
  formOptions: queryFormOptions,
  gridOptions: getGridOptions(),
});

// 添加多选相关状态和事件
const selectedRows = ref<any[]>([]);

// 处理复选框变化事件
const handleCheckboxChange = ({ records }: { records: any[] }) => {
  selectedRows.value = records.map((item) => item.id);
};

const authInterface = async () => {
  return selectedRows.value.length === 0
    ? new Promise((_resolve, reject) => {
        ElMessage.warning('请选择要授权的接口');
        reject(new Error('请选择要授权的接口'));
      })
    : authInterfaceApi({
        addInterfaceIds: selectedRows.value,
        appKey: props.appKey,
      }).then((r) => {
        ElMessage.success('授权成功');
        emit('authSuccess');
        return r;
      });
};

const QueryGridRef = ref();

const confirmAndContinue = async () => {
  await authInterface().then(() => {
    selectedRows.value = [];
    gridApi.grid.clearCheckboxRow();
  });
};

const confirm = async () => {
  await authInterface().then(() => {
    modalApi.close();
  });
};
const gatewayAuthInfoMap = useAuthGateway();
</script>

<template>
  <ElButton type="primary" @click="modalApi.open()">添加授权接口</ElButton>
  <Modal class="w-[1200px]" :title="title">
    <QueryGrid
      ref="QueryGridRef"
      :grid-events="{
        checkboxAll: handleCheckboxChange,
        checkboxChange: handleCheckboxChange,
      }"
    >
      <template #authInfoList="{ row }">
        <div
          v-for="(item, idx) in row.authInfoList"
          :key="idx"
          class="mb-[6px]"
        >
          <span>{{ gatewayAuthInfoMap.authGateway[item.authGateway] }}</span>
          &nbsp;|&nbsp;
          <span>{{ gatewayAuthInfoMap.authMode[item.authMode] }}</span>

          <span v-if="item.rbacValidation">
            &nbsp;|&nbsp;
            {{ gatewayAuthInfoMap.rbacValidation[item.rbacValidation] }}
          </span>
        </div>
      </template>
    </QueryGrid>
    <template #footer>
      <div class="w-ful flex">
        <!-- <ElButton type="danger" @click="modalApi.close()">关闭</ElButton> -->
        <div class="flex flex-1 justify-end gap-2">
          <ElButton @click="confirmAndContinue()">确认并继续授权</ElButton>
          <ElButton type="primary" @click="confirm()"> 确认授权 </ElButton>
        </div>
      </div>
    </template>
  </Modal>
</template>
