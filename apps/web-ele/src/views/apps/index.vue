<script lang="ts" setup>
import type { VbenFormProps } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';

import { nextTick, ref } from 'vue';

import { ElCard } from 'element-plus';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { getAppsApi } from '#/api/apps';

import ConfigModal from './config-modal.vue';

export interface RowType {
  appKey: string; // 应用id
  appName: string; // 应用名称
  authMethod: string; // 鉴权方式
  authMethodDesc: string;
  cerKey: string; // 非对称方式鉴权密钥
  createdBy: string; // 创建人
  createdTime: string; // 创建时间
  enable: boolean; // 是否启用
  endTime: string; // 失效时间
  id: number; // 主键
  privateKey: string; // 私钥
  publicKey: string; // 公钥
  startTime: string; // 生效时间
  updatedBy: string; // 更新人
  updatedTime: string; // 更新时间
}

const editRow = ref<RowType | undefined>();
const modalRef = ref<InstanceType<typeof ConfigModal>>();
const formOptions: VbenFormProps = {
  // 默认展开
  collapsed: false,
  showCollapseButton: false,
  schema: [
    {
      component: 'Input',
      componentProps: {
        clearable: true,
        placeholder: '请输入应用编码',
      },
      fieldName: 'appKey',
      label: '应用编码',
    },
    {
      component: 'Input',
      componentProps: {
        clearable: true,
        placeholder: '请输入应用名称',
      },
      fieldName: 'appName',
      label: '应用名称',
    },
    {
      component: 'Select',
      componentProps: {
        clearable: true,
        filterOption: true,
        options: [
          {
            label: '启用',
            value: true,
          },
          {
            label: '停用',
            value: false,
          },
        ],
        placeholder: '请选择状态',
      },
      fieldName: 'enable',
      label: '是否启用',
    },
  ],
  submitButtonOptions: {
    content: '查询',
  },
  // 是否在字段值改变时提交表单
  submitOnChange: false,
  // 按下回车时是否提交表单
  submitOnEnter: false,
};

const gridOptions: VxeGridProps<RowType> = {
  minHeight: 350,
  columns: [
    {
      field: 'appKey',
      title: '应用编码',
      width: 100,
    },
    { field: 'appName', title: '应用名称' }, // 使用appName字段
    {
      field: 'enable',
      title: '是否启用',
      slots: {
        default: ({ row }) => (row.enable ? '启用' : '停用'),
      },
    },
    // { field: 'publicKey', title: '公钥' },
    // { field: 'privateKey', title: '私钥' },
    // { field: 'cerKey', title: 'CerKey' },
    { field: 'authMethodDesc', title: '鉴权方式' }, // 使用注释中的鉴权方式描述字段
    { field: 'createdTime', title: '创建时间' },
    {
      title: '有效期',
      slots: {
        default: ({ row }) =>
          row.startTime
            ? `${row.startTime} ~ ${row.endTime || '长期有效'}`
            : '',
      },
    },
    {
      title: '操作',
      slots: {
        default: 'action',
      },
    },
  ],
  keepSource: true,
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        return getAppsApi(formValues, page.currentPage, page.pageSize);
      },
    },
  },
  toolbarConfig: {
    // 是否显示搜索表单控制按钮
    // @ts-ignore 正式环境时有完整的类型声明
    // search: true,
  },
};

const [Grid, GridApi] = useVbenVxeGrid({ formOptions, gridOptions });
const editRowEvent = (row: RowType) => {
  editRow.value = row;
  nextTick(() => {
    modalRef.value?.edit(row);
    modalRef.value?.open();
  });
};

// 添加删除方法
// const handleDelete = async (row: RowType) => {
//   try {
//     // 添加确认弹窗
//     await ElMessageBox.confirm('确定要删除该应用配置吗？', '删除确认', {
//       confirmButtonText: '确定',
//       cancelButtonText: '取消',
//       type: 'warning',
//     });

//     await requestClient.get(`/hera/safe-center/b/safeConfig/del?id=${row.id}`);
//     // 删除成功后刷新表格
//     Grid.value?.commitProxy('query');
//     ElMessage.success('删除成功');
//   } catch (error) {
//     // 用户取消删除时忽略错误
//     if (error !== 'cancel') {
//       console.error('error', error);
//     }
//   }
// };

const handleSuccess = () => {
  GridApi.reload();
};

const handleCreate = () => {
  editRow.value = undefined;
  nextTick(() => {
    modalRef.value?.open();
  });
};
</script>

<template>
  <ElCard class="vp-raw w-full">
    <Grid>
      <template #toolbar>
        <ElButton class="mb-2" type="primary" @click="handleCreate">
          新建应用
        </ElButton>
        <ConfigModal
          @success="handleSuccess"
          :edit-id="editRow?.id"
          ref="modalRef"
        />
      </template>
      <template #toolbar-tools>
        <ElButton class="mr-2"> 导入 </ElButton>
        <ElButton class="mr-2"> 导出 </ElButton>
        <ElButton class="mr-2"> 批量上下线 </ElButton>
      </template>
      <template #action="{ row }">
        <ElButton size="small" type="primary" @click="editRowEvent({ ...row })">
          编辑
        </ElButton>
        <RouterLink
          class="ml-2"
          :to="{
            path: `/console/apps/${row.appKey}`,
            query: { appName: row.appName },
          }"
        >
          <ElButton size="small" type="success">详情</ElButton>
        </RouterLink>
      </template>
    </Grid>
  </ElCard>
</template>
