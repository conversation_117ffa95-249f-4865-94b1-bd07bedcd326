<script lang="ts" setup>
import { computed, onMounted, ref } from 'vue';
import { useRoute } from 'vue-router';

import { Page } from '@vben/common-ui';

import { ElDescriptions, ElInput, ElMessage, ElMessageBox } from 'element-plus';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { getAppsApi } from '#/api/apps';
import useAuthGateway from '#/views/port/list/useAuthGateway';

import { authInterfaceApi, getGridOptions, queryFormOptions } from './common';
import ConfigApiModal from './config-api-modal.vue';

const route = useRoute();
const appKey = route.params.appKey as string; // 类型断言确保是字符串类型
const appName = route.query.appName as string; // 获取查询参数并类型断言
const detailInfo = ref<any>({});
const formOptions = {
  ...queryFormOptions,
  schema: (queryFormOptions.schema || []).filter((_, index) => index === 0),
};
const copyStatus = ref(false);
let copyTimer: any;
const gridOptions = getGridOptions({
  api: '/hera/safe-center/b/safeConfig/authInterfacePage',
  apiParams: {
    appKey,
  },
});

gridOptions.columns = [
  ...(gridOptions.columns || []),

  {
    title: '操作',
    slots: {
      default: 'action',
    },
  },
];

const [QueryGrid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});

// 添加多选相关状态和事件
const selectedRows = ref<any[]>([]);

// 处理复选框变化事件
const handleCheckboxChange = ({ records }: { records: any[] }) => {
  selectedRows.value = records.map((item) => item.id);
};

// // 状态枚举获取方法
// async function getStatusEnum() {
//   // ... 保持与config-api-modal相同的枚举获取逻辑 ...
// }

const handleBatchCancel = () => {
  cancelAuthInterface(selectedRows.value, true).then(() => {
    selectedRows.value = [];
    gridApi.grid.clearCheckboxRow();
  });
};

async function handleCopy(text: string) {
  try {
    await navigator.clipboard.writeText(text);
    copyStatus.value = true; // 这里更新状态
    ElMessage.success('复制成功');

    // 3秒后重置状态
    copyTimer && clearTimeout(copyTimer);
    copyTimer = setTimeout(() => {
      copyStatus.value = false;
    }, 3000);
  } catch {
    ElMessage.success('复制失败');
  }
}

function cancelAuthInterface(ids: string[], isBatch = false) {
  return ElMessageBox.confirm(
    `确定要取消${isBatch ? '选中接口的' : ''}授权吗？`,
    '操作确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    },
  )
    .then(() => {
      return authInterfaceApi({
        appKey,
        delInterfaceIds: ids,
        addInterfaceIds: [],
      });
    })
    .then(() => {
      ElMessage.success(`${isBatch ? '批量' : ''}取消授权成功`);
      gridApi.reload();
    })
    .catch(() => {
      ElMessage.info(`已取消${isBatch ? '批量' : ''}操作`);
    });
}

const handleAuthSuccess = () => {
  gridApi.reload();
};

const keys = computed(() => {
  return [
    { name: '公钥', code: 'publicKey' },
    { name: '私钥', code: 'privateKey' },
  ].filter((item) => detailInfo.value[item.code]);
});

onMounted(async () => {
  const res = await getAppsApi({ appKey }, 1, 1);
  detailInfo.value = res.items[0];
});

const gatewayAuthInfoMap = useAuthGateway();
</script>

<template>
  <Page title="">
    <template #description>
      <div class="text-muted-foreground">
        <ElDescriptions title="应用详情" :column="4" border>
          <el-descriptions-item label="应用编码：">
            {{ appKey }}
          </el-descriptions-item>
          <el-descriptions-item label="应用名称： ">
            {{ appName }}
          </el-descriptions-item>
          <el-descriptions-item label="cerKey：">
            {{ detailInfo.cerKey || '--' }}
          </el-descriptions-item>
          <el-descriptions-item label="鉴权方式">
            {{ detailInfo.authMethodDesc || '--' }}
          </el-descriptions-item>

          <el-descriptions-item
            :key="item.code"
            v-for="item in keys"
            :span="2"
            :label="`${item.name}：`"
          >
            <div
              class="group relative cursor-pointer"
              @click="handleCopy(detailInfo[item.code])"
            >
              <ElInput
                :input-style="{
                  backgroundColor: 'var(--el-disabled-bg-color)',
                  color: 'var(--el-disabled-text-color)',
                  cursor: 'pointer',
                }"
                type="textarea"
                :readonly="true"
                resize="none"
                :value="detailInfo[item.code]"
              />
              <span
                class="text-primary invisible absolute right-0 top-0 bg-white p-1 group-hover:visible"
                :class="{ '!block text-green-500': copyStatus }"
              >
                点击即可复制
              </span>
            </div>
          </el-descriptions-item>
        </ElDescriptions>
      </div>
    </template>

    <QueryGrid
      :grid-events="{
        checkboxAll: handleCheckboxChange,
        checkboxChange: handleCheckboxChange,
      }"
    >
      <template #authInfoList="{ row }">
        <div
          v-for="(item, idx) in row.authInfoList"
          :key="idx"
          class="mb-[6px]"
        >
          <span>{{ gatewayAuthInfoMap.authGateway[item.authGateway] }}</span>
          &nbsp;|&nbsp;
          <span>{{ gatewayAuthInfoMap.authMode[item.authMode] }}</span>

          <span v-if="item.rbacValidation">
            &nbsp;|&nbsp;
            {{ gatewayAuthInfoMap.rbacValidation[item.rbacValidation] }}
          </span>
        </div>
      </template>
      <template #toolbar>
        <div class="mb-2">
          <ConfigApiModal @auth-success="handleAuthSuccess" :app-key="appKey" />
          <ElButton
            @click="handleBatchCancel"
            v-if="selectedRows.length > 0"
            type="danger"
          >
            批量取消授权
          </ElButton>
        </div>
      </template>

      <template #action="{ row }">
        <ElButton type="danger" @click="() => cancelAuthInterface([row.id])">
          取消授权
        </ElButton>
      </template>
    </QueryGrid>
  </Page>
</template>
