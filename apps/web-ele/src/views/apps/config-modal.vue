<script lang="ts" setup>
import type { RowType } from './index.vue';

import { computed } from 'vue';

import { useVbenForm, useVbenModal, z } from '@vben/common-ui';

import dayjs from 'dayjs';
import { ElMessage } from 'element-plus';

import { getSelectOptions } from '#/api/common';
import { requestClient } from '#/api/request';

const { editId } = defineProps<{
  editId: number | undefined;
}>();

const emit = defineEmits(['success']);

const [Modal, modalApi] = useVbenModal();

const isEdit = computed(() => {
  return !!editId;
});

const title = computed(() => (isEdit.value ? '编辑应用' : '新建应用'));

const createOrUpdate = (data: any) => {
  return requestClient
    .post('/hera/safe-center/b/safeConfig/saveOrUp', data)
    .then((res) => {
      console.warn(res);
    });
};

const formOptions = computed(() => ({
  showDefaultActions: false,
  schema: [
    {
      component: 'Input',
      componentProps: {
        clearable: true,
        placeholder: `请输入应用编码`,
      },
      disabled: isEdit.value,
      rules: z
        .string()
        .regex(/^[a-z0-9]{3,16}$/i, '应用编码只能是3-16位字母或数字'),
      fieldName: 'appKey',
      formFieldProps: {
        validateOnBlur: true,
        validateOnChange: false,
        validateOnInput: false,
        validateOnModelUpdate: false,
      },
      label: '应用编码',
    },
    {
      component: 'Input',
      componentProps: {
        clearable: true,
        placeholder: '请输入应用名称',
      },
      rules: 'required',
      fieldName: 'appName',
      label: '应用名称',
    },

    {
      component: 'Select',
      componentProps: {
        clearable: true,
        filterOption: true,
        options: [
          {
            label: '启用',
            value: true,
          },
          {
            label: '停用',
            value: false,
          },
        ],
        placeholder: '请选择是否启用',
      },
      rules: 'required',
      fieldName: 'enable',
      label: '是否启用',
    },
    {
      component: 'ApiSelect',
      componentProps: {
        clearable: true,
        placeholder: '请选择鉴权方式',
        api: getSelectOptions('authmethod'),
      },
      rules: 'required',
      fieldName: 'authMethod',
      label: '鉴权方式',
    },
    {
      component: 'Input',
      componentProps: {
        clearable: true,
        placeholder: '请输入应用CerKey',
      },
      fieldName: 'cerKey',
      label: 'CerKey',
      help: '非RSA鉴权方式的密钥或token，不填可自动生成',
      dependencies: {
        show(values: RowType) {
          if (!values?.authMethod) return false;
          return values?.authMethod.indexOf('RSA') === -1;
        },
        triggerFields: ['authMethod'],
      },
    },
    {
      component: 'DatePicker',
      defaultValue: dayjs().toDate(),
      componentProps: {
        type: 'datetime',
        clearable: true,
        placeholder: '请选择生效日期',
      },
      rules: 'required',
      fieldName: 'startTime',
      label: '生效日期',
    },
    {
      component: 'DatePicker',
      componentProps: {
        type: 'datetime',
        clearable: true,
        placeholder: '请选择失效日期',
      },
      fieldName: 'endTime',
      label: '失效日期',
    },
  ],
}));

const [Form, formApi] = useVbenForm(formOptions.value);

const handleSubmit = () => {
  formApi.validateAndSubmitForm().then((formData) => {
    if (formData) {
      createOrUpdate({
        ...formData,
        id: editId,
        startTime: formData.startTime
          ? dayjs(formData.startTime).format('YYYY-MM-DD 00:00:00')
          : undefined,
        endTime: formData.endTime
          ? dayjs(formData.endTime).format('YYYY-MM-DD 00:00:00')
          : undefined,
      }).then(() => {
        ElMessage.success('操作成功');
        modalApi.close();
        emit('success');
      });
    }
  });
};

defineExpose({
  edit: (data: RowType) => {
    formApi.setValues(data);
  },
  open: () => {
    modalApi.open();
  },
});
</script>

<template>
  <Modal class="w-[600px]" :title="title">
    <Form />
    <template #footer>
      <div class="flex flex-1 items-center">
        <el-link
          target="_blank"
          :underline="false"
          href="https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=65486101"
        >
          <span
            class="icon-[mdi--alert-circle] text-muted-foreground mr-1 size-4"
          ></span>
          应用验签方式说明
        </el-link>
        <div class="flex flex-1 justify-end gap-2">
          <ElButton @click="modalApi.close()">取消</ElButton>
          <ElButton type="primary" @click="handleSubmit()">提交</ElButton>
        </div>
      </div>
    </template>
  </Modal>
</template>
