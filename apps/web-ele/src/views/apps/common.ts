import type { VbenFormProps } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';

import { getSelectOptions } from '#/api/common';
import { requestClient } from '#/api/request';

export const getApplicationNameOptions = async (keyword: string) => {
  const res = await requestClient.get(
    '/hera/safe-center/b/safeInterface/listApplicationName',
    {
      params: {
        keyword,
      },
    },
  );

  return res.map((name: string) => ({
    label: name,
    value: name,
  }));
};

export const queryFormOptions: VbenFormProps = {
  collapsed: false,
  showCollapseButton: false,
  schema: [
    {
      component: 'ApiSelect',
      componentProps: {
        placeholder: '请输入',
        options: [],
        clearable: true,
        filterable: true,
        api: getApplicationNameOptions,
      },
      fieldName: 'applicationName',
      label: '服务名',
    },
    {
      component: 'ApiSelect',
      componentProps: {
        filterable: true,
        clearable: true,
        api: getSelectOptions('interfacestatus'),
      },
      fieldName: 'status',
      label: '状态',
    },
    {
      component: 'Input',

      componentProps: { placeholder: '请输入', clearable: true },
      fieldName: 'apiClass',
      label: 'API类',
    },
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入path路径',
        clearable: true,
      },
      fieldName: 'apiPath',
      label: 'path路径',
    },
  ],
};

export const authInterfaceApi = (data: any) => {
  return requestClient
    .post('/hera/safe-center/b/safeConfig/authInterfaceUp', data)
    .then((res) => {
      return res;
    });
};

export const getGridOptions = (
  params?: {
    api?: string;
    apiParams?: any;
  } = {},
): VxeGridProps => {
  const { api, apiParams = {} } = params;
  return {
    minHeight: 350,
    showOverflow: false,
    columns: [
      { type: 'checkbox', width: 60 },
      { field: 'applicationName', title: '服务名' },
      { field: 'apiClass', title: 'API类' },
      { field: 'apiPathList', title: '接口路径' },
      { field: 'apiWayList', title: '请求方式' },

      { field: 'apiDesc', title: '接口描述' },
      {
        field: 'authInfoList',
        title: '调用网关',
        slots: { default: 'authInfoList' },
        minWidth: 200,
      },
      { field: 'statusDesc', title: '状态' },
    ],
    proxyConfig: {
      ajax: {
        query: async ({ page }, formValues) => {
          const res = await requestClient.get(
            // '/hera/safe-center/b/safeConfig/authInterfacePage',
            api || '/hera/safe-center/b/safeInterface/page',
            {
              params: {
                currentPage: page.currentPage,
                pageSize: page.pageSize,
                ...formValues,
                ...apiParams,
              },
            },
          );
          return {
            total: res.totalCount,
            items: res.data,
          };
        },
      },
    },
    checkboxConfig: {
      checkField: 'checked',
      trigger: 'cell',
    },
  };
};
