<script lang="ts" setup>
import type { VbenFormProps } from '@vben/common-ui';

import type { VxeGridProps } from '#/adapter/vxe-table';

import { computed, ref, watch } from 'vue';

import { InfoFilled } from '@element-plus/icons-vue';
import { ElButton, ElDialog } from 'element-plus';

import { useVbenVxeGrid } from '#/adapter/vxe-table';

import { createToolSelectionFormOptions } from '../config/form-config';
import { createToolSelectionGridOptions } from '../config/grid-config';

// 可用工具接口定义
export interface AvailableTool {
  id: string;
  name: string;
  displayName: string;
  description: string;
}

interface Props {
  visible: boolean;
  selectedToolIds: string[];
  selectedToolsData?: AvailableTool[]; // 已选择的完整工具数据
}

interface Emits {
  (e: 'update:visible', value: boolean): void;
  (e: 'confirm', selectedTools: AvailableTool[]): void;
  (e: 'cancel'): void;
}

const props = defineProps<Props>();

const emit = defineEmits<Emits>();

// 临时选中的工具（对话框中的选择）
const tempSelectedTools = ref<AvailableTool[]>([]);

// 计算属性：弹窗显示状态
const dialogVisible = computed({
  get: () => props.visible,
  set: (value: boolean) => {
    emit('update:visible', value);
    if (!value) {
      emit('cancel');
    }
  },
});

// 工具选择表格配置
const toolSelectionFormOptions: VbenFormProps =
  createToolSelectionFormOptions();

const toolSelectionGridOptions: VxeGridProps<AvailableTool> =
  createToolSelectionGridOptions();

const [ToolSelectionGrid, ToolSelectionGridApi] = useVbenVxeGrid({
  formOptions: toolSelectionFormOptions,
  gridOptions: toolSelectionGridOptions,
});

// 设置表格选中状态的函数
const setTableSelection = () => {
  if (!ToolSelectionGridApi?.grid) return;

  try {
    // 获取当前表格中的所有数据
    const tableData = ToolSelectionGridApi.grid.getTableData().fullData;

    if (!tableData || tableData.length === 0) {
      // 如果数据还没加载完成，稍后重试
      setTimeout(() => {
        setTableSelection();
      }, 100);
      return;
    }

    // 清除所有选中状态
    ToolSelectionGridApi.grid.clearCheckboxRow();

    // 根据已选择的工具ID设置选中状态
    const selectedIds = props.selectedToolIds;
    const selectedRows: any[] = [];

    tableData.forEach((row: any) => {
      if (selectedIds.includes(row.id)) {
        ToolSelectionGridApi.grid.setCheckboxRow(row, true);
        selectedRows.push(row);
      }
    });

    // 更新 tempSelectedTools，保持其他页面的选择，只更新当前页面的选择
    const currentPageIds = tableData.map((row: any) => row.id);
    const otherPagesTools = tempSelectedTools.value.filter(
      (tool) => !currentPageIds.includes(tool.id),
    );
    tempSelectedTools.value = [...otherPagesTools, ...selectedRows];
  } catch (error) {
    console.warn('设置表格选中状态失败:', error);
  }
};

// 处理对话框中的选择变化
const handleDialogCheckboxChange = (event: any) => {
  const currentPageRecords = event.records || [];

  // 获取当前表格中的所有数据
  const tableData = ToolSelectionGridApi?.grid?.getTableData().fullData || [];
  const currentPageIds = tableData.map((row: any) => row.id);

  // 移除当前页面的所有工具（无论是否选中）
  const otherPagesTools = tempSelectedTools.value.filter(
    (tool) => !currentPageIds.includes(tool.id),
  );

  // 合并其他页面的选择和当前页面的选择
  tempSelectedTools.value = [...otherPagesTools, ...currentPageRecords];
};

// 处理代理查询完成事件
const handleProxyQuery = () => {
  // 数据加载完成后，设置选中状态
  setTimeout(() => {
    setTableSelection();
  }, 50);
};

// 确认选择
const confirmSelection = () => {
  // tempSelectedTools.value 已经包含了所有页面的选择，直接使用即可
  emit('confirm', tempSelectedTools.value);
  dialogVisible.value = false;
};

// 取消选择
const cancelSelection = () => {
  tempSelectedTools.value = [];
  dialogVisible.value = false;
  emit('cancel');
};

// 监听弹窗打开，初始化选中状态
watch(
  () => props.visible,
  (newVisible) => {
    if (newVisible) {
      // 初始化 tempSelectedTools，使用已选择的完整工具数据
      tempSelectedTools.value = props.selectedToolsData
        ? [...props.selectedToolsData]
        : [];

      // 设置表格中的选中状态，需要延迟执行以确保表格数据已加载
      setTimeout(() => {
        setTableSelection();
      }, 500); // 增加延迟时间，确保 proxyConfig 数据加载完成
    }
  },
);

// 监听已选择的工具ID变化，更新表格选中状态
watch(
  () => props.selectedToolIds,
  () => {
    if (props.visible) {
      setTimeout(() => {
        setTableSelection();
      }, 100);
    }
  },
  { deep: true },
);
</script>

<template>
  <ElDialog
    v-model="dialogVisible"
    title="选择工具"
    width="80%"
    :close-on-click-modal="false"
    destroy-on-close
  >
    <!-- 对话框内容 - VxeGrid表格 -->
    <div class="dialog-content">
      <div class="vp-raw h-[500px] w-full">
        <ToolSelectionGrid
          :grid-events="{
            checkboxChange: handleDialogCheckboxChange,
            checkboxAll: handleDialogCheckboxChange,
            proxyQuery: handleProxyQuery,
          }"
        >
          <!-- 自定义空状态插槽 -->
          <template #empty>
            <div class="flex h-full flex-col items-center justify-center py-16">
              <div class="mb-4 text-6xl text-gray-400">
                <InfoFilled />
              </div>
              <div class="mb-2 text-lg font-medium text-gray-500">暂无数据</div>
              <div class="text-sm text-gray-400">
                请尝试调整搜索条件或稍后再试
              </div>
            </div>
          </template>
        </ToolSelectionGrid>
      </div>
    </div>

    <!-- 对话框底部按钮 -->
    <template #footer>
      <div class="dialog-footer">
        <span class="mr-4 text-sm text-gray-500">
          已选择 {{ tempSelectedTools.length }} 个工具
        </span>
        <ElButton @click="cancelSelection">取消</ElButton>
        <ElButton type="primary" @click="confirmSelection"> 确定 </ElButton>
      </div>
    </template>
  </ElDialog>
</template>

<style scoped>
/* 使用Tailwind CSS，无需额外样式 */
</style>
