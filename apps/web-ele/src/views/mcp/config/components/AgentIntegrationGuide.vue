<!--
 * @Description: Agent集成指南组件
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-07-24
-->
<script lang="ts" setup>
import { computed } from 'vue';

import { CopyDocument } from '@element-plus/icons-vue';
import { ElButton } from 'element-plus';

// Props
interface Props {
  groupId?: string;
  groupName?: string;
  selectedToolIds?: string[];
}

// 工具信息接口
interface ToolInfo {
  name: string;
  displayName: string;
  category: string;
}

// 工具分组接口
interface ToolGroup {
  category: string;
  tools: string[];
}

const props = withDefaults(defineProps<Props>(), {
  groupId: '',
  groupName: '',
  selectedToolIds: () => [],
});

// 计算属性
const hasGroupInfo = computed(() => props.groupId && props.groupName);

// 复制代码到剪贴板
const copyToClipboard = async (text: string) => {
  try {
    await navigator.clipboard.writeText(text);
    // 这里可以添加成功提示
  } catch (error) {
    console.error('复制失败:', error);
  }
};

// 生成Agent配置代码示例
const agentConfigExample = computed(() => {
  if (!hasGroupInfo.value) return '';

  return `{
  "agent_id": "customer_service_agent_001",
  "name": "智能客服助手",
  "tool_groups": ["${props.groupId}"], // 使用工具分组ID
  "model": "gpt-4",
  "capabilities": ["订单查询", "商品咨询", "库存查询"]
}`;
});

// 工具ID到工具信息的映射
const toolsMapping: Record<string, ToolInfo> = {
  // 订单管理相关工具
  listOrders: {
    name: 'listOrders',
    displayName: '查询订单列表',
    category: '订单管理',
  },
  getOrderDetail: {
    name: 'getOrderDetail',
    displayName: '获取订单详情',
    category: '订单管理',
  },
  createOrder: {
    name: 'createOrder',
    displayName: '创建订单',
    category: '订单管理',
  },
  updateOrderStatus: {
    name: 'updateOrderStatus',
    displayName: '更新订单状态',
    category: '订单管理',
  },
  cancelOrder: {
    name: 'cancelOrder',
    displayName: '取消订单',
    category: '订单管理',
  },

  // 商品查询相关工具
  searchProducts: {
    name: 'searchProducts',
    displayName: '搜索商品',
    category: '商品查询',
  },
  getProductDetail: {
    name: 'getProductDetail',
    displayName: '获取商品详情',
    category: '商品查询',
  },
  checkInventory: {
    name: 'checkInventory',
    displayName: '查询库存',
    category: '商品查询',
  },

  // 用户服务相关工具
  getUserInfo: {
    name: 'getUserInfo',
    displayName: '获取用户信息',
    category: '用户服务',
  },
  updateUserProfile: {
    name: 'updateUserProfile',
    displayName: '更新用户资料',
    category: '用户服务',
  },
  getUserOrders: {
    name: 'getUserOrders',
    displayName: '获取用户订单',
    category: '用户服务',
  },
};

// 获取已选择工具的分类列表
const selectedToolsExample = computed((): ToolGroup[] => {
  if (props.selectedToolIds.length === 0) return [];

  // 根据实际选择的工具ID获取工具信息
  const selectedTools: ToolInfo[] = [];

  props.selectedToolIds.forEach((id) => {
    const tool = toolsMapping[id];
    if (tool) {
      selectedTools.push(tool);
    }
  });

  // 按类别分组
  const groupedTools: Record<string, string[]> = {};

  selectedTools.forEach((tool) => {
    const category = tool.category;
    if (!groupedTools[category]) {
      groupedTools[category] = [];
    }
    groupedTools[category].push(tool.name);
  });

  // 转换为数组格式
  return Object.entries(groupedTools).map(([category, tools]) => ({
    category,
    tools,
  }));
});
</script>

<template>
  <div class="agent-integration-guide card">
    <h3 class="card-title">Agent集成指南</h3>

    <!-- 步骤1: 在Agent配置中引用工具分组 -->
    <div class="preview-box">
      <div class="preview-title">1. 在Agent配置中引用工具分组</div>
      <div v-if="hasGroupInfo" class="code-editor-container">
        <div class="code-header">
          <span class="text-sm font-medium">配置示例:</span>
          <ElButton size="small" @click="copyToClipboard(agentConfigExample)">
            <template #icon>
              <el-icon size="14">
                <CopyDocument />
              </el-icon>
            </template>
            复制
          </ElButton>
        </div>
        <div class="code-editor">{{ agentConfigExample }}</div>
      </div>
      <div v-else class="empty-state">
        <p class="text-sm text-yellow-700">请先完成分组创建和工具选择</p>
      </div>
    </div>

    <!-- 步骤2: Agent将自动获得分组内所有工具的访问权限 -->
    <div class="preview-box" style="margin-top: 1rem">
      <div class="preview-title">
        2. Agent将自动获得分组内所有工具的访问权限
      </div>
      <div
        v-if="hasGroupInfo && selectedToolsExample.length > 0"
        class="tools-content"
      >
        <strong>{{ groupId }} 包含的工具：</strong>
        <ul class="tools-list">
          <li
            v-for="toolGroup in selectedToolsExample"
            :key="toolGroup.category"
          >
            {{ toolGroup.category }}：{{ toolGroup.tools.join(', ') }}
          </li>
        </ul>
      </div>
      <div v-else class="empty-state">
        <p class="text-sm text-gray-500">选择工具后将显示工具列表</p>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* 基础卡片样式 - 匹配HTML设计 */
.card {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 1rem;
}

/* 预览框样式 - 完全匹配HTML设计 */
.preview-box {
  background: #f8fafc;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  padding: 1rem;
}

.preview-title {
  font-weight: 600;
  color: #475569;
  margin-bottom: 0.5rem;
  font-size: 13px;
}

/* 代码编辑器样式 - 完全匹配HTML设计 */
.code-editor-container {
  margin-top: 1rem;
}

.code-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.code-editor {
  font-family: 'Monaco', 'Consolas', monospace;
  background: #1e293b;
  color: #e2e8f0;
  padding: 1rem;
  border-radius: 6px;
  font-size: 13px;
  line-height: 1.5;
  overflow-x: auto;
  white-space: pre;
}

/* 工具内容样式 - 匹配HTML设计 */
.tools-content {
  padding: 1rem;
  background: white;
  border-radius: 4px;
  margin-top: 0.5rem;
}

.tools-list {
  margin-top: 0.5rem;
  margin-left: 1.5rem;
  line-height: 1.8;
  list-style-type: disc;
}

.tools-list li {
  margin-bottom: 0.25rem;
}

/* 空状态样式 */
.empty-state {
  padding: 1rem;
  background: #fef3c7;
  border: 1px solid #fde68a;
  border-radius: 6px;
  margin-top: 0.5rem;
}

/* 按钮样式调整 */
:deep(.el-button) {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 13px;
  padding: 0.375rem 0.875rem;
}

/* 组件整体样式 */
.agent-integration-guide {
  margin-top: 1.5rem;
}
</style>
