/*
 * @Author: <PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-07-24 17:45:00
 * @LastEditors: zhangqian <EMAIL>
 * @LastEditTime: 2025-07-25 11:07:39
 * @Description: 工具分组管理组合式函数
 * @FilePath: /dev-platform-ui/apps/web-ele/src/views/mcp/config/composables/useToolGroups.ts
 */

import type { CreateGroupForm, ToolGroup } from '../types';

import { computed, reactive, ref } from 'vue';

import { ElMessage, ElMessageBox } from 'element-plus';

import { GroupIdApi, ToolGroupsApi } from '../api/tool-groups';
import {
  DEFAULT_PAGE_SIZE,
  LOADING_STATES,
  OPERATION_MESSAGES,
  TOOL_GROUP_PREFIX,
} from '../constants';
import { handleError } from '../utils/error-handler';

// 工具分组状态接口
interface ToolGroupState {
  toolGroups: ToolGroup[];
  loading: boolean;
  createLoading: boolean;
  deleteLoading: boolean;
  generateIdLoading: boolean;
  currentPage: number;
  pageSize: number;
  total: number;
  searchParams: {
    description?: string;
    groupName?: string;
  };
}

// 工具分组管理组合式函数
export function useToolGroups() {
  // 状态管理
  const state = reactive<ToolGroupState>({
    toolGroups: [],
    loading: false,
    createLoading: false,
    deleteLoading: false,
    generateIdLoading: false,
    currentPage: 1,
    pageSize: DEFAULT_PAGE_SIZE,
    total: 0,
    searchParams: {},
  });

  // 表单显示状态
  const showCreateForm = ref(false);
  const editingGroup = ref<null | ToolGroup>(null);

  // 表格引用，用于刷新数据
  const gridRef = ref<any>(null);

  // 刷新表格数据
  const refreshGrid = () => {
    if (gridRef.value?.grid) {
      gridRef.value.grid.commitProxy('query');
    }
  };

  // 计算属性：是否正在编辑
  const isEditing = computed(() => !!editingGroup.value);

  // 计算属性：表单标题
  const formTitle = computed(() =>
    isEditing.value ? '编辑分组' : '创建新分组',
  );

  // 计算属性：是否有数据
  const hasData = computed(() => state.toolGroups.length > 0);

  // 计算属性：加载状态
  const loadingState = computed(() => {
    if (state.loading) return LOADING_STATES.LOADING;
    if (hasData.value) return LOADING_STATES.SUCCESS;
    return LOADING_STATES.IDLE;
  });

  // 加载管理器 - 暂时注释掉以排查问题
  // const loadingManager = useLoadingManager();

  // 加载工具分组列表
  const loadToolGroups = async (showLoading: boolean = true) => {
    if (showLoading) {
      state.loading = true;
    }

    try {
      const params = {
        ...state.searchParams,
        page: state.currentPage,
        pageSize: state.pageSize,
      };

      const response = await ToolGroupsApi.getToolGroups(params);

      if (response.success) {
        state.toolGroups = response.data.items;
        state.total = response.data.total;
        return response.data;
      }

      throw new Error('获取数据失败');
    } catch (error) {
      handleError(error, { operation: '加载工具分组列表' });
      return null;
    } finally {
      state.loading = false;
    }
  };

  // 搜索工具分组 - 现在由表格的 proxyConfig 自动处理
  const searchToolGroups = (searchParams: {
    description?: string;
    groupName?: string;
  }) => {
    state.searchParams = searchParams;
    // 表格会自动处理搜索，无需手动调用 loadToolGroups
  };

  // 重置搜索 - 现在由表格的 proxyConfig 自动处理
  const resetSearch = () => {
    state.searchParams = {};
    // 表格会自动处理重置，无需手动调用 loadToolGroups
  };

  // 生成分组ID
  const generateGroupId = async (): Promise<string> => {
    state.generateIdLoading = true;

    try {
      const response = await GroupIdApi.generateGroupId();

      if (response.success) {
        ElMessage.success(OPERATION_MESSAGES.ID_GENERATE_SUCCESS);
        return response.data;
      }

      throw new Error('生成ID失败');
    } catch (error) {
      handleError(error, { operation: '生成分组ID' });
      // 返回默认ID作为后备
      const timestamp = Date.now().toString().slice(-4);
      const random = Math.floor(Math.random() * 100)
        .toString()
        .padStart(2, '0');
      return `${TOOL_GROUP_PREFIX}${timestamp}${random}`;
    } finally {
      state.generateIdLoading = false;
    }
  };

  // 创建工具分组
  const createToolGroup = async (
    formData: CreateGroupForm,
  ): Promise<boolean> => {
    state.createLoading = true;

    try {
      const response = await ToolGroupsApi.createToolGroup(formData);

      if (response.success) {
        ElMessage.success(OPERATION_MESSAGES.CREATE_SUCCESS);
        refreshGrid(); // 刷新表格数据
        return true;
      }

      throw new Error('创建失败');
    } catch (error) {
      handleError(error, { operation: '创建工具分组' });
      return false;
    } finally {
      state.createLoading = false;
    }
  };

  // 更新工具分组
  const updateToolGroup = async (
    id: string,
    formData: Partial<CreateGroupForm>,
  ): Promise<boolean> => {
    state.createLoading = true;

    try {
      const response = await ToolGroupsApi.updateToolGroup(id, formData);

      if (response.success) {
        ElMessage.success(OPERATION_MESSAGES.UPDATE_SUCCESS);
        refreshGrid();
        return true;
      }

      throw new Error('更新失败');
    } catch (error) {
      handleError(error, { operation: '更新工具分组' });
      return false;
    } finally {
      state.createLoading = false;
    }
  };

  // 删除工具分组
  const deleteToolGroup = async (group: ToolGroup): Promise<boolean> => {
    try {
      await ElMessageBox.confirm(
        `确定要删除分组"${group.groupName}"吗？此操作不可恢复。`,
        '确认删除',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        },
      );

      state.deleteLoading = true;

      const response = await ToolGroupsApi.deleteToolGroup(group.id);

      if (response.success) {
        ElMessage.success(OPERATION_MESSAGES.DELETE_SUCCESS);
        refreshGrid();
        return true;
      }

      throw new Error('删除失败');
    } catch (error) {
      if (error !== 'cancel') {
        handleError(error, { operation: '删除工具分组' });
      }
      return false;
    } finally {
      state.deleteLoading = false;
    }
  };

  // 批量删除工具分组
  const batchDeleteToolGroups = async (
    groupIds: string[],
  ): Promise<boolean> => {
    try {
      await ElMessageBox.confirm(
        `确定要删除选中的 ${groupIds.length} 个分组吗？此操作不可恢复。`,
        '确认批量删除',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        },
      );

      state.deleteLoading = true;

      const response = await ToolGroupsApi.batchDeleteToolGroups(groupIds);

      if (response.success) {
        ElMessage.success(`成功删除 ${groupIds.length} 个分组`);
        refreshGrid();
        return true;
      }

      throw new Error('批量删除失败');
    } catch (error) {
      if (error !== 'cancel') {
        handleError(error, { operation: '批量删除工具分组' });
      }
      return false;
    } finally {
      state.deleteLoading = false;
    }
  };

  // 显示创建表单
  const showCreateGroupForm = async () => {
    editingGroup.value = null;
    showCreateForm.value = true;
  };

  // 显示编辑表单
  const showEditGroupForm = (group: ToolGroup) => {
    editingGroup.value = group;
    showCreateForm.value = true;
  };

  // 隐藏表单
  const hideForm = () => {
    showCreateForm.value = false;
    editingGroup.value = null;
  };

  // 复制分组ID到剪贴板
  const copyGroupId = async (groupId: string) => {
    try {
      await navigator.clipboard.writeText(groupId);
      ElMessage.success(`${OPERATION_MESSAGES.COPY_SUCCESS}: ${groupId}`);
    } catch {
      ElMessage.error(OPERATION_MESSAGES.COPY_FAILED);
    }
  };

  // 分页现在由表格的 proxyConfig 自动处理，无需手动处理

  return {
    // 状态
    state,
    showCreateForm,
    editingGroup,

    // 计算属性
    isEditing,
    formTitle,
    hasData,
    loadingState,

    // 方法
    loadToolGroups,
    searchToolGroups,
    resetSearch,
    generateGroupId,
    createToolGroup,
    updateToolGroup,
    deleteToolGroup,
    batchDeleteToolGroups,
    showCreateGroupForm,
    showEditGroupForm,
    hideForm,
    copyGroupId,
    refreshGrid,
    gridRef,
  };
}
