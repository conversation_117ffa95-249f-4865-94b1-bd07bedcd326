/*
 * @Author: <PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-07-24 17:45:00
 * @LastEditors: zhangqian <EMAIL>
 * @LastEditTime: 2025-07-25 09:43:58
 * @Description: 表单验证组合式函数
 * @FilePath: /dev-platform-ui/apps/web-ele/src/views/mcp/config/composables/useFormValidation.ts
 */

import type { CreateGroupForm } from '../types';

import { computed, reactive, ref } from 'vue';

import {
  getInitialFormValues,
  validateField,
  validateForm,
} from '../config/form-config';

// 表单验证状态接口
interface ValidationState {
  isValid: boolean;
  errors: Record<string, string>;
  touched: Record<string, boolean>;
}

// 表单验证组合式函数
export function useFormValidation() {
  // 表单数据
  const formData = reactive<CreateGroupForm>(getInitialFormValues());

  // 验证状态
  const validationState = reactive<ValidationState>({
    isValid: false,
    errors: {},
    touched: {},
  });

  // 是否正在验证
  const isValidating = ref(false);

  // 计算属性：表单是否有效
  const isFormValid = computed(() => {
    return (
      Object.keys(validationState.errors).length === 0 &&
      formData.groupName.trim() !== '' &&
      formData.selectedToolIds.length > 0
    );
  });

  // 计算属性：是否有错误
  const hasErrors = computed(() => {
    return Object.keys(validationState.errors).length > 0;
  });

  // 计算属性：错误数量
  const errorCount = computed(() => {
    return Object.keys(validationState.errors).length;
  });

  // 验证单个字段
  const validateSingleField = (
    fieldName: string,
    value: any,
  ): null | string => {
    const error = validateField(fieldName, value);

    if (error) {
      validationState.errors[fieldName] = error;
    } else {
      delete validationState.errors[fieldName];
    }

    validationState.touched[fieldName] = true;
    return error;
  };

  // 验证整个表单
  const validateFormData = (): boolean => {
    isValidating.value = true;

    try {
      const errors = validateForm(formData);
      validationState.errors = errors;
      validationState.isValid = Object.keys(errors).length === 0;

      // 标记所有字段为已触摸
      Object.keys(formData).forEach((key) => {
        validationState.touched[key] = true;
      });

      return validationState.isValid;
    } finally {
      isValidating.value = false;
    }
  };

  // 清除验证错误
  const clearValidationErrors = (fieldName?: string) => {
    if (fieldName) {
      delete validationState.errors[fieldName];
      validationState.touched[fieldName] = false;
    } else {
      validationState.errors = {};
      validationState.touched = {};
    }
  };

  // 重置表单
  const resetForm = () => {
    Object.assign(formData, getInitialFormValues());
    clearValidationErrors();
    validationState.isValid = false;
  };

  // 设置表单数据
  const setFormData = (data: Partial<CreateGroupForm>) => {
    Object.assign(formData, data);
  };

  // 获取字段错误信息
  const getFieldError = (fieldName: string): null | string => {
    return validationState.errors[fieldName] || null;
  };

  // 检查字段是否有错误
  const hasFieldError = (fieldName: string): boolean => {
    return !!validationState.errors[fieldName];
  };

  // 检查字段是否已触摸
  const isFieldTouched = (fieldName: string): boolean => {
    return !!validationState.touched[fieldName];
  };

  // 标记字段为已触摸
  const touchField = (fieldName: string) => {
    validationState.touched[fieldName] = true;
  };

  // 验证分组名称
  const validateGroupName = (value: string): null | string => {
    return validateSingleField('groupName', value);
  };

  // 验证描述
  const validateDescription = (value: string): null | string => {
    return validateSingleField('description', value);
  };

  // 验证选中的工具
  const validateSelectedTools = (value: string[]): null | string => {
    return validateSingleField('selectedToolIds', value);
  };

  // 实时验证
  const enableRealTimeValidation = () => {
    // 监听表单数据变化，实时验证
    const stopWatchers: (() => void)[] = [];

    // 这里可以添加 watch 来监听表单字段变化
    // 由于这是组合式函数，实际的 watch 应该在使用的组件中设置

    return () => {
      stopWatchers.forEach((stop) => stop());
    };
  };

  // 获取所有错误信息
  const getAllErrors = (): string[] => {
    return Object.values(validationState.errors);
  };

  // 获取第一个错误信息
  const getFirstError = (): null | string => {
    const errors = getAllErrors();
    return errors.length > 0 ? errors[0] || null : null;
  };

  // 检查必填字段是否完整
  const checkRequiredFields = (): boolean => {
    const requiredFields = ['groupName', 'selectedToolIds'];
    return requiredFields.every((field) => {
      const value = formData[field as keyof CreateGroupForm];
      if (Array.isArray(value)) {
        return value.length > 0;
      }
      return value && value.toString().trim() !== '';
    });
  };

  // 获取验证摘要
  const getValidationSummary = () => {
    return {
      isValid: isFormValid.value,
      hasErrors: hasErrors.value,
      errorCount: errorCount.value,
      requiredFieldsComplete: checkRequiredFields(),
      errors: validationState.errors,
      touched: validationState.touched,
    };
  };

  return {
    // 数据
    formData,
    validationState,
    isValidating,

    // 计算属性
    isFormValid,
    hasErrors,
    errorCount,

    // 方法
    validateSingleField,
    validateFormData,
    clearValidationErrors,
    resetForm,
    setFormData,
    getFieldError,
    hasFieldError,
    isFieldTouched,
    touchField,
    validateGroupName,
    validateDescription,
    validateSelectedTools,
    enableRealTimeValidation,
    getAllErrors,
    getFirstError,
    checkRequiredFields,
    getValidationSummary,
  };
}
