/*
 * @Author: <PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-07-24 17:45:00
 * @LastEditors: zhangqian <EMAIL>
 * @LastEditTime: 2025-07-25 11:04:17
 * @Description: 工具选择组合式函数
 * @FilePath: /dev-platform-ui/apps/web-ele/src/views/mcp/config/composables/useToolSelection.ts
 */

import type { AvailableTool } from '../types';

import { computed, ref, watch } from 'vue';

import { ElMessage, ElMessageBox } from 'element-plus';

import { AvailableToolsApi } from '../api/tool-groups';
import { DEFAULT_PAGE_SIZE, DELAYS, OPERATION_MESSAGES } from '../constants';

// 工具选择状态接口
interface ToolSelectionState {
  selectedToolIds: string[];
  selectedToolsData: AvailableTool[]; // 保存完整的已选择工具数据
  availableTools: AvailableTool[];
  tempSelectedTools: AvailableTool[];
  searchQuery: string;
  currentPage: number;
  pageSize: number;
  total: number;
  loading: boolean;
  hasMore: boolean;
}

// 工具选择组合式函数
export function useToolSelection() {
  // 状态管理
  const state = ref<ToolSelectionState>({
    selectedToolIds: [],
    selectedToolsData: [],
    availableTools: [],
    tempSelectedTools: [],
    searchQuery: '',
    currentPage: 1,
    pageSize: DEFAULT_PAGE_SIZE,
    total: 0,
    loading: false,
    hasMore: true,
  });

  // 对话框显示状态
  const dialogVisible = ref(false);

  // 搜索防抖定时器
  let searchTimer: NodeJS.Timeout | null = null;

  // 计算属性：已选择工具数量
  const selectedCount = computed(() => state.value.selectedToolIds.length);

  // 计算属性：已选择的工具详情
  const selectedTools = computed(() => {
    return state.value.selectedToolsData;
  });

  // 计算属性：临时选择的工具数量
  const tempSelectedCount = computed(
    () => state.value.tempSelectedTools.length,
  );

  // 计算属性：是否有选择的工具
  const hasSelectedTools = computed(() => selectedCount.value > 0);

  // 计算属性：是否正在加载
  const isLoading = computed(() => state.value.loading);

  // 加载可用工具
  const loadAvailableTools = async (reset: boolean = false) => {
    if (state.value.loading) return;

    state.value.loading = true;

    try {
      const params = {
        toolName: state.value.searchQuery,
        page: reset ? 1 : state.value.currentPage,
        pageSize: state.value.pageSize,
      };

      const response = await AvailableToolsApi.getAvailableTools(params);

      if (response.success) {
        if (reset) {
          state.value.availableTools = response.data.items;
          state.value.currentPage = 1;
        } else {
          state.value.availableTools.push(...response.data.items);
        }

        state.value.total = response.data.total;
        state.value.hasMore =
          state.value.availableTools.length < response.data.total;
      }
    } catch (error) {
      console.error('加载工具失败:', error);
      ElMessage.error('加载工具失败，请稍后重试');
    } finally {
      state.value.loading = false;
    }
  };

  // 搜索工具（防抖）
  const searchTools = (query: string) => {
    state.value.searchQuery = query;

    if (searchTimer) {
      clearTimeout(searchTimer);
    }

    searchTimer = setTimeout(() => {
      loadAvailableTools(true);
    }, DELAYS.DEBOUNCE_SEARCH);
  };

  // 加载更多工具
  const loadMoreTools = async () => {
    if (!state.value.hasMore || state.value.loading) return;

    state.value.currentPage += 1;
    await loadAvailableTools(false);
  };

  // 打开工具选择对话框
  const openToolSelectionDialog = () => {
    dialogVisible.value = true;

    // 初始化临时选择状态
    state.value.tempSelectedTools = [...selectedTools.value];
  };

  // 关闭工具选择对话框
  const closeToolSelectionDialog = () => {
    dialogVisible.value = false;
    state.value.tempSelectedTools = [];
    state.value.searchQuery = '';
  };

  // 处理工具选择变化（在对话框中）
  const handleToolSelectionChange = (selectedTools: AvailableTool[]) => {
    state.value.tempSelectedTools = selectedTools;
  };

  // 确认工具选择
  const confirmToolSelection = (selectedCount?: number) => {
    const count = selectedCount ?? state.value.tempSelectedTools.length;
    if (selectedCount === undefined) {
      // 如果没有传入数量，使用 tempSelectedTools 的数据
      state.value.selectedToolIds = state.value.tempSelectedTools.map(
        (tool) => tool.id,
      );
    }
    closeToolSelectionDialog();
    ElMessage.success(
      `${OPERATION_MESSAGES.TOOL_SELECTION_SUCCESS}: ${count} 个工具`,
    );
  };

  // 取消工具选择
  const cancelToolSelection = () => {
    closeToolSelectionDialog();
  };

  // 移除已选择的工具
  const removeSelectedTool = async (tool: AvailableTool) => {
    try {
      await ElMessageBox.confirm(
        `确定要删除 "${tool.displayName}" 吗？`,
        '确认删除',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        },
      );

      const index = state.value.selectedToolIds.indexOf(tool.id);
      if (index !== -1) {
        state.value.selectedToolIds.splice(index, 1);
        // 同时从 selectedToolsData 中移除
        const dataIndex = state.value.selectedToolsData.findIndex(
          (t) => t.id === tool.id,
        );
        if (dataIndex !== -1) {
          state.value.selectedToolsData.splice(dataIndex, 1);
        }
        ElMessage.success(OPERATION_MESSAGES.TOOL_REMOVE_SUCCESS);
      }
    } catch {
      // 用户取消删除
    }
  };

  // 批量移除工具
  const batchRemoveTools = async (toolIds: string[]) => {
    try {
      await ElMessageBox.confirm(
        `确定要删除选中的 ${toolIds.length} 个工具吗？`,
        '确认批量删除',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        },
      );

      state.value.selectedToolIds = state.value.selectedToolIds.filter(
        (id) => !toolIds.includes(id),
      );

      ElMessage.success(`成功删除 ${toolIds.length} 个工具`);
    } catch {
      // 用户取消删除
    }
  };

  // 清空所有选择
  const clearAllSelections = async () => {
    if (state.value.selectedToolIds.length === 0) return;

    try {
      await ElMessageBox.confirm(`确定要清空所有已选择的工具吗？`, '确认清空', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      });

      const count = state.value.selectedToolIds.length;
      state.value.selectedToolIds = [];
      ElMessage.success(`已清空 ${count} 个工具`);
    } catch {
      // 用户取消清空
    }
  };

  // 设置选中的工具
  const setSelectedTools = (toolIds: string[]) => {
    state.value.selectedToolIds = [...toolIds];
  };

  // 设置选中的工具完整数据
  const setSelectedToolsData = (tools: AvailableTool[]) => {
    state.value.selectedToolsData = [...tools];
    state.value.selectedToolIds = tools.map((tool) => tool.id);
  };

  // 添加工具到选择列表
  const addToolToSelection = (tool: AvailableTool) => {
    if (!state.value.selectedToolIds.includes(tool.id)) {
      state.value.selectedToolIds.push(tool.id);
      ElMessage.success(`已添加工具: ${tool.displayName}`);
    }
  };

  // 检查工具是否已选择
  const isToolSelected = (toolId: string): boolean => {
    return state.value.selectedToolIds.includes(toolId);
  };

  // 切换工具选择状态
  const toggleToolSelection = (tool: AvailableTool) => {
    const index = state.value.selectedToolIds.indexOf(tool.id);
    if (index === -1) {
      state.value.selectedToolIds.push(tool.id);
    } else {
      state.value.selectedToolIds.splice(index, 1);
    }
  };

  // 获取工具选择统计信息
  const getSelectionStats = () => {
    return {
      selectedCount: selectedCount.value,
      totalAvailable: state.value.total,
      selectionRate:
        state.value.total > 0
          ? ((selectedCount.value / state.value.total) * 100).toFixed(1)
          : '0',
    };
  };

  // 重置选择状态
  const resetSelection = () => {
    state.value.selectedToolIds = [];
    state.value.selectedToolsData = [];
    state.value.tempSelectedTools = [];
    state.value.searchQuery = '';
    state.value.currentPage = 1;
  };

  // 处理滚动加载
  const handleScroll = () => {
    console.warn('滚动加载更多工具');
    loadMoreTools();
  };

  // 监听对话框关闭，清理状态
  watch(dialogVisible, (visible) => {
    if (!visible) {
      state.value.tempSelectedTools = [];
      state.value.searchQuery = '';
    }
  });

  return {
    // 状态
    state,
    dialogVisible,

    // 计算属性
    selectedCount,
    selectedTools,
    tempSelectedCount,
    hasSelectedTools,
    isLoading,

    // 方法
    loadAvailableTools,
    searchTools,
    loadMoreTools,
    openToolSelectionDialog,
    closeToolSelectionDialog,
    handleToolSelectionChange,
    confirmToolSelection,
    cancelToolSelection,
    removeSelectedTool,
    batchRemoveTools,
    clearAllSelections,
    setSelectedTools,
    setSelectedToolsData,
    addToolToSelection,
    isToolSelected,
    toggleToolSelection,
    getSelectionStats,
    resetSelection,
    handleScroll,
  };
}
