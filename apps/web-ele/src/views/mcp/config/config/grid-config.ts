/*
 * @Author: <PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-07-24 17:45:00
 * @LastEditors: zhang<PERSON>an <EMAIL>
 * @LastEditTime: 2025-07-25 15:31:00
 * @Description: MCP配置管理表格配置
 * @FilePath: /dev-platform-ui/apps/web-ele/src/views/mcp/config/config/grid-config.ts
 */

import type { AvailableTool, ToolGroup } from '../types';

import type { VxeGridProps } from '#/adapter/vxe-table';

import { formatDate } from '#/views/prompt/utils';

import { AvailableToolsApi, ToolGroupsApi } from '../api/tool-groups';
import {
  DEFAULT_PAGE_SIZE,
  PAGE_SIZES,
  TABLE_COLUMNS,
  TABLE_MAX_HEIGHT,
  TABLE_MIN_HEIGHT,
} from '../constants';

// 工具分组表格配置工厂函数
export const createToolGroupGridOptions = (): VxeGridProps<ToolGroup> => ({
  minHeight: TABLE_MIN_HEIGHT,
  columns: [
    {
      field: TABLE_COLUMNS.GROUP_ID,
      title: '分组ID',
      width: 180,
      slots: {
        default: 'groupId',
      },
    },
    {
      field: TABLE_COLUMNS.GROUP_NAME,
      title: '分组名称',
      minWidth: 150,
    },
    {
      field: TABLE_COLUMNS.TOOLS,
      title: '包含工具',
      minWidth: 300,
      slots: {
        default: 'tools',
      },
    },
    {
      field: TABLE_COLUMNS.TOOL_COUNT,
      title: '工具数量',
      width: 100,
      align: 'center',
      slots: {
        default: 'toolCount',
      },
    },
    {
      field: TABLE_COLUMNS.CREATED_AT,
      title: '创建时间',
      width: 120,
      formatter: ({ cellValue }) => {
        return formatDate(cellValue) || '-';
      },
    },
    {
      title: '操作',
      width: 200,
      fixed: 'right',
      slots: {
        default: 'action',
      },
    },
  ],
  keepSource: true,
  pagerConfig: {
    pageSize: DEFAULT_PAGE_SIZE,
    pageSizes: PAGE_SIZES,
  },
  border: true,
  stripe: true,
  showOverflow: 'tooltip',
  showHeaderOverflow: 'tooltip',
  resizable: true,
  sortConfig: {
    trigger: 'cell',
    remote: true,
  },
  filterConfig: {
    remote: true,
  },
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        try {
          // 使用真实API调用
          const { currentPage, pageSize } = page;
          const params = {
            groupName: formValues?.groupName, // 分组名称搜索
            description: formValues?.description, // 描述搜索
            page: currentPage,
            pageSize,
          };

          // 调用API获取数据
          const result = await ToolGroupsApi.getToolGroups(params);

          // 将API返回的数据转换为表格需要的格式
          if (result.success && result.data) {
            const { items, total } = result.data;
            return {
              items,
              total,
            };
          }
          throw new Error('API返回数据格式不正确');
        } catch (error) {
          console.error('获取工具分组列表失败:', error);
          return {
            items: [],
            total: 0,
          };
        }
      },
    },
  },
});

// 工具选择表格配置工厂函数
export const createToolSelectionGridOptions =
  (): VxeGridProps<AvailableTool> => ({
    minHeight: TABLE_MIN_HEIGHT,
    maxHeight: TABLE_MAX_HEIGHT,
    checkboxConfig: {
      highlight: true,
      range: true,
      reserve: true,
    },
    columns: [
      {
        type: 'checkbox',
        width: 50,
        fixed: 'left',
      },
      {
        field: 'name',
        title: '工具名称',
        minWidth: 150,
        sortable: true,
      },
      {
        field: 'displayName',
        title: '显示名称',
        minWidth: 150,
        sortable: true,
      },
      {
        field: 'description',
        title: '描述',
        minWidth: 250,
        showOverflow: 'tooltip',
      },
    ],
    keepSource: true,
    pagerConfig: {
      pageSize: DEFAULT_PAGE_SIZE,
      pageSizes: PAGE_SIZES,
    },
    border: true,
    stripe: true,
    showOverflow: 'tooltip',
    showHeaderOverflow: 'tooltip',
    resizable: true,
    sortConfig: {
      trigger: 'cell',
      multiple: true,
    },
    filterConfig: {
      remote: true,
    },
    toolbarConfig: {
      refresh: true,
      zoom: true,
      custom: true,
    },
    proxyConfig: {
      ajax: {
        query: async ({ page }, formValues) => {
          try {
            // 使用真实API调用
            const { currentPage, pageSize } = page;
            const params = {
              toolName: formValues?.toolName, // 工具名称搜索
              // description: formValues?.description, // 描述搜索
              page: currentPage,
              pageSize,
            };

            // 调用API获取数据
            const result = await AvailableToolsApi.getAvailableTools(params);

            // 将API返回的数据转换为表格需要的格式
            if (result.success && result.data) {
              const { items, total } = result.data;
              return {
                items,
                total,
              };
            }
            throw new Error('API返回数据格式不正确');
          } catch (error) {
            console.error('获取可用工具列表失败:', error);
            return {
              items: [],
              total: 0,
            };
          }
        },
      },
    },
  });

// 已选择工具表格配置
export const createSelectedToolsTableConfig = () => ({
  border: true,
  stripe: true,
  showOverflow: 'tooltip',
  emptyText: '暂无选择的工具',
  size: 'small' as const,
});

// 表格工具栏配置
export const createToolbarConfig = () => ({
  refresh: {
    query: () => {
      // 刷新数据的回调
    },
  },
  zoom: true,
  custom: true,
  slots: {
    buttons: 'toolbar_buttons',
  },
});

// 表格分页配置
export const createPagerConfig = (
  currentPage: number = 1,
  pageSize: number = DEFAULT_PAGE_SIZE,
  total: number = 0,
) => ({
  currentPage,
  pageSize,
  total,
  pageSizes: PAGE_SIZES,
  layouts: [
    'PrevJump',
    'PrevPage',
    'Number',
    'NextPage',
    'NextJump',
    'Sizes',
    'Total',
  ],
  perfect: true,
});

// 表格排序配置
export const createSortConfig = (remote: boolean = true) => ({
  trigger: 'cell' as const,
  remote,
  multiple: true,
  chronological: true,
});

// 表格筛选配置
export const createFilterConfig = (remote: boolean = true) => ({
  remote,
  showIcon: true,
  iconNone: 'vxe-icon-funnel',
  iconMatch: 'vxe-icon-funnel',
});

// 表格复选框配置
export const createCheckboxConfig = (reserve: boolean = false) => ({
  highlight: true,
  range: true,
  reserve,
  labelField: 'name',
  checkMethod: ({ row }: { row: any }) => {
    console.warn(row);
    // 可以在这里添加行选择的条件判断
    return true;
  },
});

// 表格编辑配置
export const createEditConfig = (trigger: 'click' | 'dblclick' = 'click') => ({
  trigger,
  mode: 'row' as const,
  showStatus: true,
  showUpdateStatus: true,
  showInsertStatus: true,
  autoClear: true,
});

// 表格验证配置
export const createValidConfig = () => ({
  autoPos: true,
  showMessage: true,
  message: 'inline' as const,
});

// 表格键盘配置
export const createKeyboardConfig = () => ({
  isArrow: true,
  isDel: true,
  isEnter: true,
  isTab: true,
  isEdit: true,
});

// 表格鼠标配置
export const createMouseConfig = () => ({
  selected: true,
  hover: true,
});

// 表格滚动配置
export const createScrollConfig = (x?: number, y?: number) => ({
  x,
  y,
  scrollToRightOnChange: true,
  scrollToTopOnChange: true,
});

// 表格导出配置
export const createExportConfig = () => ({
  filename: '工具分组列表',
  sheetName: 'Sheet1',
  type: 'xlsx' as const,
  types: ['xlsx', 'csv', 'html', 'xml', 'txt'],
  modes: ['current', 'selected', 'all'],
  columns: [
    { field: 'groupId', title: '分组ID' },
    { field: 'groupName', title: '分组名称' },
    { field: 'description', title: '描述' },
    { field: 'toolCount', title: '工具数量' },
    { field: 'createdAt', title: '创建时间' },
  ],
});

// 表格打印配置
export const createPrintConfig = () => ({
  columns: [
    { field: 'groupId', title: '分组ID' },
    { field: 'groupName', title: '分组名称' },
    { field: 'description', title: '描述' },
    { field: 'toolCount', title: '工具数量' },
    { field: 'createdAt', title: '创建时间' },
  ],
});
