/*
 * @Author: <PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-07-24 17:45:00
 * @LastEditors: z<PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-07-24 18:16:19
 * @Description: MCP配置管理模拟数据
 * @FilePath: /dev-platform-ui/apps/web-ele/src/views/mcp/config/config/mock-data.ts
 */

import type { AvailableTool, ToolGroup } from '../types';

// 模拟工具分组数据
export const mockToolGroups: ToolGroup[] = [
  {
    id: 'tg_5a7b2c9f',
    groupId: 'TG_ECOMMERCE_001',
    groupName: '电商助手',
    description: '用于电商场景，提供订单查询、商品信息等功能',
    toolIds: ['tool_001', 'tool_002', 'tool_003'],
    toolCount: 12,
    createdAt: '2024-01-15',
    tools: [
      { id: 'tool_001', name: 'listOrders', displayName: '查询订单列表' },
      { id: 'tool_002', name: 'getOrderDetail', displayName: '查询订单详情' },
      { id: 'tool_003', name: 'searchProducts', displayName: '搜索商品' },
      { id: 'tool_005', name: 'checkInventory', displayName: '查询库存' },
      { id: 'tool_006', name: 'updateStock', displayName: '更新库存' },
      { id: 'tool_007', name: 'getStockAlert', displayName: '库存预警' },
    ],
  },
  {
    id: 'tg_6b8c3d0a',
    groupId: 'TG_CUSTOMER_002',
    groupName: '客服工具',
    description: '用于客服场景，提供用户信息查询、订单处理等功能',
    toolIds: ['tool_001', 'tool_002', 'tool_004'],
    toolCount: 8,
    createdAt: '2024-01-10',
    tools: [
      { id: 'tool_001', name: 'listOrders', displayName: '查询订单列表' },
      { id: 'tool_002', name: 'getOrderDetail', displayName: '查询订单详情' },
      { id: 'tool_004', name: 'getUserInfo', displayName: '获取用户信息' },
    ],
  },
  {
    id: 'tg_7c9d4e1b',
    groupId: 'TG_INVENTORY_003',
    groupName: '库存管理',
    description: '用于库存管理场景，提供库存查询、更新等功能',
    toolIds: ['tool_005', 'tool_006', 'tool_007'],
    toolCount: 5,
    createdAt: '2024-01-08',
    tools: [
      { id: 'tool_005', name: 'checkInventory', displayName: '查询库存' },
      { id: 'tool_006', name: 'updateStock', displayName: '更新库存' },
      { id: 'tool_007', name: 'getStockAlert', displayName: '库存预警' },
    ],
  },
];

// 模拟可用工具数据
export const mockAvailableTools: AvailableTool[] = [
  {
    id: 'tool_001',
    name: 'listOrders',
    displayName: '查询订单列表',
    description: '查询订单列表，支持按日期范围、订单状态等条件筛选',
  },
  {
    id: 'tool_002',
    name: 'getOrderDetail',
    displayName: '查询订单详情',
    description: '获取指定订单的完整信息',
  },
  {
    id: 'tool_003',
    name: 'searchProducts',
    displayName: '搜索商品',
    description: '搜索商品信息',
  },
  {
    id: 'tool_004',
    name: 'getUserInfo',
    displayName: '获取用户信息',
    description: '获取用户基本信息',
  },
  {
    id: 'tool_005',
    name: 'checkInventory',
    displayName: '查询库存',
    description: '查询商品库存信息',
  },
  {
    id: 'tool_006',
    name: 'updateStock',
    displayName: '更新库存',
    description: '更新商品库存数量',
  },
  {
    id: 'tool_007',
    name: 'getStockAlert',
    displayName: '库存预警',
    description: '获取库存预警信息',
  },
  {
    id: 'tool_008',
    name: 'createOrder',
    displayName: '创建订单',
    description: '创建新的订单',
  },
  {
    id: 'tool_009',
    name: 'updateOrderStatus',
    displayName: '更新订单状态',
    description: '更新订单的状态信息',
  },
  {
    id: 'tool_010',
    name: 'cancelOrder',
    displayName: '取消订单',
    description: '取消指定的订单',
  },
  {
    id: 'tool_011',
    name: 'getProductDetail',
    displayName: '获取商品详情',
    description: '获取商品的详细信息',
  },
  {
    id: 'tool_012',
    name: 'updateProductInfo',
    displayName: '更新商品信息',
    description: '更新商品的基本信息',
  },
  {
    id: 'tool_013',
    name: 'getCustomerProfile',
    displayName: '获取客户档案',
    description: '获取客户的详细档案信息',
  },
  {
    id: 'tool_014',
    name: 'sendNotification',
    displayName: '发送通知',
    description: '向用户发送各类通知消息',
  },
  {
    id: 'tool_015',
    name: 'generateReport',
    displayName: '生成报表',
    description: '生成各类业务报表',
  },
];

// 工具分类映射
export const toolCategoryMapping = {
  订单管理: ['tool_001', 'tool_002', 'tool_008', 'tool_009', 'tool_010'],
  商品管理: ['tool_003', 'tool_011', 'tool_012'],
  库存管理: ['tool_005', 'tool_006', 'tool_007'],
  客户服务: ['tool_004', 'tool_013', 'tool_014'],
  数据分析: ['tool_015'],
};

// 获取工具分类
export const getToolCategory = (toolId: string): string => {
  for (const [category, toolIds] of Object.entries(toolCategoryMapping)) {
    if (toolIds.includes(toolId)) {
      return category;
    }
  }
  return '其他';
};

// 根据分类获取工具
export const getToolsByCategory = (category: string): AvailableTool[] => {
  const toolIds =
    toolCategoryMapping[category as keyof typeof toolCategoryMapping] || [];
  return mockAvailableTools.filter((tool) => toolIds.includes(tool.id));
};

// 搜索工具
export const searchTools = (query: string): AvailableTool[] => {
  if (!query.trim()) {
    return mockAvailableTools;
  }

  const lowerQuery = query.toLowerCase();
  return mockAvailableTools.filter(
    (tool) =>
      tool.name.toLowerCase().includes(lowerQuery) ||
      tool.displayName.toLowerCase().includes(lowerQuery) ||
      tool.description.toLowerCase().includes(lowerQuery),
  );
};

// 分页获取工具
export const getPaginatedTools = (
  tools: AvailableTool[],
  page: number,
  pageSize: number,
): { items: AvailableTool[]; total: number } => {
  const start = (page - 1) * pageSize;
  const end = start + pageSize;

  return {
    items: tools.slice(start, end),
    total: tools.length,
  };
};
