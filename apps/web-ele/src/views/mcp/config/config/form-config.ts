/*
 * @Author: <PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-07-24 17:45:00
 * @LastEditors: zhang<PERSON>an <EMAIL>
 * @LastEditTime: 2025-07-25 14:02:23
 * @Description: MCP配置管理表单配置
 * @FilePath: /dev-platform-ui/apps/web-ele/src/views/mcp/config/config/form-config.ts
 */

import type { VbenFormProps } from '#/adapter/form';

import {
  FORM_FIELDS,
  MAX_DESCRIPTION_LENGTH,
  MAX_GROUP_NAME_LENGTH,
  MIN_GROUP_NAME_LENGTH,
  REGEX_PATTERNS,
  VALIDATION_MESSAGES,
} from '../constants';

// 表单验证规则
export const formValidationRules = {
  [FORM_FIELDS.GROUP_NAME]: [
    {
      required: true,
      message: VALIDATION_MESSAGES.REQUIRED_GROUP_NAME,
      trigger: 'blur',
    },
    {
      min: MIN_GROUP_NAME_LENGTH,
      max: MAX_GROUP_NAME_LENGTH,
      message: `长度在 ${MIN_GROUP_NAME_LENGTH} 到 ${MAX_GROUP_NAME_LENGTH} 个字符`,
      trigger: 'blur',
    },
    {
      pattern: REGEX_PATTERNS.GROUP_NAME,
      message: VALIDATION_MESSAGES.INVALID_GROUP_NAME_PATTERN,
      trigger: 'blur',
    },
  ],
  [FORM_FIELDS.DESCRIPTION]: [
    {
      max: MAX_DESCRIPTION_LENGTH,
      message: VALIDATION_MESSAGES.DESCRIPTION_TOO_LONG,
      trigger: 'blur',
    },
  ],
  [FORM_FIELDS.SELECTED_TOOL_IDS]: [
    {
      required: true,
      message: VALIDATION_MESSAGES.REQUIRED_TOOLS,
      trigger: 'change',
      validator: (_rule: any, value: string[]) => {
        if (!value || !Array.isArray(value) || value.length === 0) {
          return false;
        }
        return true;
      },
    },
  ],
};

// 查询表单配置工厂函数
export const createSearchFormOptions = (
  onSubmit: (values: any) => void,
  onReset: () => void,
): VbenFormProps => ({
  schema: [
    {
      component: 'Input',
      componentProps: {
        placeholder: '搜索分组名称...',
        clearable: true,
      },
      fieldName: 'groupName',
      label: '分组名称',
    },
    {
      component: 'Input',
      componentProps: {
        placeholder: '搜索描述...',
        clearable: true,
      },
      fieldName: 'description',
      label: '描述',
    },
  ],
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
  },
  handleSubmit: onSubmit,
  handleReset: onReset,
  layout: 'horizontal',
  wrapperClass: 'grid-cols-1 md:grid-cols-2',
  collapsed: false,
  showCollapseButton: false,
});

// 创建/编辑分组表单配置
export const createEditFormOptions = (): VbenFormProps => ({
  layout: 'vertical',
  schema: [
    {
      component: 'Input',
      componentProps: {
        placeholder: '例如:智能客服工具',
        clearable: true,
      },
      fieldName: 'groupName',
      label: '分组名称',
    },
    {
      component: 'Input',
      fieldName: 'groupId',
      label: '分组ID(自动生成)',
      componentProps: {
        readonly: true,
      },
    },
    {
      component: 'Input',
      componentProps: {
        placeholder: '请选择工具',
        clearable: true,
        readonly: true,
      },
      fieldName: 'selectedToolIds',
      label: '选择工具',
    },
    {
      component: 'Input',
      componentProps: {
        placeholder: '描述此分组的用途...',
        clearable: true,
        type: 'textarea',
        rows: 5,
        maxlength: 200,
        showWordLimit: true,
      },
      fieldName: 'description',
      label: '分组描述',
    },
  ],
});

// 工具选择对话框表单配置
export const createToolSelectionFormOptions = (): VbenFormProps => ({
  schema: [
    {
      component: 'Input',
      componentProps: {
        placeholder: '搜索工具名称...',
        clearable: true,
      },
      fieldName: 'toolName',
      label: '工具名称',
    },
    // {
    //   component: 'Input',
    //   componentProps: {
    //     placeholder: '搜索描述...',
    //     clearable: true,
    //   },
    //   fieldName: 'description',
    //   label: '描述',
    // },
  ],
  commonConfig: {
    componentProps: {
      class: 'w-full',
    },
  },
  layout: 'horizontal',
  wrapperClass: 'grid-cols-1 md:grid-cols-2',
  collapsed: false,
  showCollapseButton: false,
});

// 表单初始值
export const getInitialFormValues = () => ({
  [FORM_FIELDS.GROUP_NAME]: '',
  [FORM_FIELDS.GROUP_ID]: '',
  [FORM_FIELDS.DESCRIPTION]: '',
  [FORM_FIELDS.SELECTED_TOOL_IDS]: [] as string[],
});

// 验证单个字段
export const validateField = (fieldName: string, value: any): null | string => {
  const rules =
    formValidationRules[fieldName as keyof typeof formValidationRules];
  if (!rules) return null;

  for (const rule of rules) {
    // 检查必填项
    if (
      'required' in rule &&
      rule.required &&
      (!value || (Array.isArray(value) && value.length === 0))
    ) {
      return rule.message;
    }

    // 检查最小长度
    if (
      'min' in rule &&
      rule.min &&
      typeof value === 'string' &&
      value.length < rule.min
    ) {
      return rule.message;
    }

    // 检查最大长度
    if (
      'max' in rule &&
      rule.max &&
      typeof value === 'string' &&
      value.length > rule.max
    ) {
      return rule.message;
    }

    // 检查正则表达式
    if (
      'pattern' in rule &&
      rule.pattern &&
      typeof value === 'string' &&
      !rule.pattern.test(value)
    ) {
      return rule.message;
    }

    // 检查自定义验证器
    if ('validator' in rule && rule.validator && !rule.validator(rule, value)) {
      return rule.message;
    }
  }

  return null;
};

// 验证整个表单
export const validateForm = (
  formData: Record<string, any>,
): Record<string, string> => {
  const errors: Record<string, string> = {};

  Object.keys(formValidationRules).forEach((fieldName) => {
    const error = validateField(fieldName, formData[fieldName]);
    if (error) {
      errors[fieldName] = error;
    }
  });

  return errors;
};
