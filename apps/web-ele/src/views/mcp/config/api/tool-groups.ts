/*
 * @Author: <PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-07-24 17:45:00
 * @LastEditors: zhang<PERSON>an <EMAIL>
 * @LastEditTime: 2025-07-25 13:50:40
 * @Description: MCP配置管理API服务
 * @FilePath: /dev-platform-ui/apps/web-ele/src/views/mcp/config/api/tool-groups.ts
 */

import type {
  ApiResponse,
  AvailableTool,
  AvailableToolListResponse,
  CreateGroupForm,
  ToolGroup,
  ToolGroupListResponse,
} from '../types';

import {
  getPaginatedTools,
  mockAvailableTools,
  mockToolGroups,
  searchTools,
} from '../config/mock-data';
import { DELAYS, ERROR_TYPES } from '../constants';

// 查询参数接口
export interface QueryParams {
  groupName?: string;
  description?: string;
  page?: number;
  pageSize?: number;
  sortField?: string;
  sortOrder?: 'asc' | 'desc';
}

// 工具查询参数接口
export interface ToolQueryParams {
  toolName?: string;
  description?: string;
  category?: string;
  page?: number;
  pageSize?: number;
}

// 自定义错误类
class ApiError extends Error {
  code: string;

  constructor(code: string, message: string) {
    super(message);
    this.code = code;
    this.name = 'ApiError';
  }
}

// 错误处理函数
const handleApiError = (error: any, operation: string): never => {
  console.error(`${operation}失败:`, error);

  // 模拟不同类型的错误
  const errorType = Math.random();
  if (errorType < 0.1) {
    throw new ApiError(ERROR_TYPES.NETWORK_ERROR, '网络连接失败');
  } else if (errorType < 0.2) {
    throw new ApiError(ERROR_TYPES.PERMISSION_ERROR, '权限不足');
  } else if (errorType < 0.3) {
    throw new ApiError(ERROR_TYPES.VALIDATION_ERROR, '数据验证失败');
  } else {
    throw new ApiError(ERROR_TYPES.SERVER_ERROR, '服务器内部错误');
  }
};

// 模拟API延迟
const simulateApiDelay = (
  delay: number = DELAYS.API_SIMULATION,
): Promise<void> => {
  return new Promise((resolve) => setTimeout(resolve, delay));
};

// 工具分组API服务类
export const ToolGroupsApi = {
  // 获取工具分组列表
  async getToolGroups(
    params: QueryParams = {},
  ): Promise<ToolGroupListResponse> {
    try {
      await simulateApiDelay();

      let filteredGroups = [...mockToolGroups];

      // 应用搜索过滤
      if (params.groupName) {
        const searchTerm = params.groupName.toLowerCase();
        filteredGroups = filteredGroups.filter((group) =>
          group.groupName.toLowerCase().includes(searchTerm),
        );
      }

      if (params.description) {
        const searchTerm = params.description.toLowerCase();
        filteredGroups = filteredGroups.filter((group) =>
          group.description.toLowerCase().includes(searchTerm),
        );
      }

      // 应用排序
      if (params.sortField && params.sortOrder) {
        filteredGroups.sort((a, b) => {
          const aValue = a[params.sortField as keyof ToolGroup];
          const bValue = b[params.sortField as keyof ToolGroup];

          // 安全的比较，处理 undefined 值
          if (aValue === undefined && bValue === undefined) return 0;
          if (aValue === undefined) return 1;
          if (bValue === undefined) return -1;

          if (params.sortOrder === 'asc') {
            return aValue > bValue ? 1 : -1;
          } else {
            return aValue < bValue ? 1 : -1;
          }
        });
      }

      // 应用分页
      const page = params.page || 1;
      const pageSize = params.pageSize || 10;
      const start = (page - 1) * pageSize;
      const end = start + pageSize;
      const paginatedGroups = filteredGroups.slice(start, end);

      return {
        code: 200,
        message: '获取成功',
        success: true,
        data: {
          items: paginatedGroups,
          total: filteredGroups.length,
        },
      };
    } catch (error) {
      return handleApiError(error, '获取工具分组列表');
    }
  },

  // 创建工具分组
  async createToolGroup(
    data: CreateGroupForm,
  ): Promise<ApiResponse<ToolGroup>> {
    try {
      await simulateApiDelay();

      // 模拟验证
      if (!data.groupName.trim()) {
        throw new ApiError(ERROR_TYPES.VALIDATION_ERROR, '分组名称不能为空');
      }

      if (data.selectedToolIds.length === 0) {
        throw new ApiError(
          ERROR_TYPES.VALIDATION_ERROR,
          '至少需要选择一个工具',
        );
      }

      // 检查分组ID是否已存在
      const existingGroup = mockToolGroups.find(
        (group) => group.groupId === data.groupId,
      );
      if (existingGroup) {
        throw new ApiError(ERROR_TYPES.VALIDATION_ERROR, '分组ID已存在');
      }

      const newGroup: ToolGroup = {
        id: `tg_${Date.now()}`,
        groupId: data.groupId,
        groupName: data.groupName,
        description: data.description || '',
        toolIds: data.selectedToolIds,
        toolCount: data.selectedToolIds.length,
        createdAt: new Date().toISOString().split('T')[0] as string,
        tools: mockAvailableTools
          .filter((tool) => data.selectedToolIds.includes(tool.id))
          .map((tool) => ({
            id: tool.id,
            name: tool.name,
            displayName: tool.displayName,
          })),
      };

      mockToolGroups.unshift(newGroup);

      return {
        code: 200,
        message: '创建成功',
        success: true,
        data: newGroup,
      };
    } catch (error) {
      return handleApiError(error, '创建工具分组');
    }
  },

  // 更新工具分组
  async updateToolGroup(
    id: string,
    data: Partial<CreateGroupForm>,
  ): Promise<ApiResponse<ToolGroup>> {
    try {
      await simulateApiDelay();

      const groupIndex = mockToolGroups.findIndex((group) => group.id === id);
      if (groupIndex === -1) {
        throw new ApiError(ERROR_TYPES.VALIDATION_ERROR, '分组不存在');
      }

      const existingGroup = mockToolGroups[groupIndex];
      if (!existingGroup) {
        throw new ApiError(ERROR_TYPES.VALIDATION_ERROR, '分组不存在');
      }

      const updatedGroup: ToolGroup = {
        ...existingGroup,
        groupName: data.groupName || existingGroup.groupName,
        groupId: data.groupId || existingGroup.groupId,
        description:
          data.description === undefined
            ? existingGroup.description
            : data.description,
        toolIds: data.selectedToolIds || existingGroup.toolIds,
        toolCount: data.selectedToolIds?.length || existingGroup.toolCount,
        tools: data.selectedToolIds
          ? mockAvailableTools
              .filter(
                (tool) => data.selectedToolIds?.includes(tool.id) ?? false,
              )
              .map((tool) => ({
                id: tool.id,
                name: tool.name,
                displayName: tool.displayName,
              }))
          : existingGroup.tools,
      };

      mockToolGroups[groupIndex] = updatedGroup;

      return {
        code: 200,
        message: '更新成功',
        success: true,
        data: updatedGroup,
      };
    } catch (error) {
      return handleApiError(error, '更新工具分组');
    }
  },

  // 删除工具分组
  async deleteToolGroup(id: string): Promise<ApiResponse> {
    try {
      await simulateApiDelay();

      const groupIndex = mockToolGroups.findIndex((group) => group.id === id);
      if (groupIndex === -1) {
        throw new ApiError(ERROR_TYPES.VALIDATION_ERROR, '分组不存在');
      }

      mockToolGroups.splice(groupIndex, 1);

      return {
        code: 200,
        message: '删除成功',
        success: true,
        data: null,
      };
    } catch (error) {
      return handleApiError(error, '删除工具分组');
    }
  },

  // 批量删除工具分组
  async batchDeleteToolGroups(ids: string[]): Promise<ApiResponse> {
    try {
      await simulateApiDelay();

      let deletedCount = 0;
      for (const id of ids) {
        const index = mockToolGroups.findIndex((group) => group.id === id);
        if (index !== -1) {
          mockToolGroups.splice(index, 1);
          deletedCount++;
        }
      }

      return {
        code: 200,
        message: `成功删除 ${deletedCount} 个分组`,
        success: true,
        data: { deletedCount },
      };
    } catch (error) {
      return handleApiError(error, '批量删除工具分组');
    }
  },
};

// 可用工具API服务类
export const AvailableToolsApi = {
  // 获取可用工具列表
  async getAvailableTools(
    params: ToolQueryParams = {},
  ): Promise<AvailableToolListResponse> {
    try {
      await simulateApiDelay(DELAYS.API_SIMULATION / 2);

      let filteredTools = [...mockAvailableTools];

      // 应用搜索过滤
      if (params.toolName || params.description) {
        const query =
          `${params.toolName || ''} ${params.description || ''}`.trim();
        filteredTools = searchTools(query);
      }

      // 应用分页
      const page = params.page || 1;
      const pageSize = params.pageSize || 20;
      const paginatedResult = getPaginatedTools(filteredTools, page, pageSize);

      return {
        code: 200,
        message: '获取成功',
        success: true,
        data: paginatedResult,
      };
    } catch (error) {
      return handleApiError(error, '获取可用工具列表');
    }
  },

  // 根据ID获取工具详情
  async getToolById(id: string): Promise<ApiResponse<AvailableTool>> {
    try {
      await simulateApiDelay(DELAYS.API_SIMULATION / 4);

      const tool = mockAvailableTools.find((t) => t.id === id);
      if (!tool) {
        throw new ApiError(ERROR_TYPES.VALIDATION_ERROR, '工具不存在');
      }

      return {
        code: 200,
        message: '获取成功',
        success: true,
        data: tool,
      };
    } catch (error) {
      return handleApiError(error, '获取工具详情');
    }
  },
};

// 生成分组ID的API
export const GroupIdApi = {
  // 生成新的分组ID
  async generateGroupId(): Promise<ApiResponse<string>> {
    try {
      await simulateApiDelay(DELAYS.ID_GENERATION);

      const timestamp = Date.now().toString().slice(-4);
      const random = Math.floor(Math.random() * 100)
        .toString()
        .padStart(2, '0');
      const groupId = `TG_CUSTOM_${timestamp}${random}`;

      return {
        code: 200,
        message: 'ID生成成功',
        success: true,
        data: groupId,
      };
    } catch (error) {
      return handleApiError(error, '生成分组ID');
    }
  },

  // 验证分组ID是否可用
  async validateGroupId(groupId: string): Promise<ApiResponse<boolean>> {
    try {
      await simulateApiDelay(DELAYS.ID_GENERATION / 2);

      const exists = mockToolGroups.some((group) => group.groupId === groupId);

      return {
        code: 200,
        message: exists ? 'ID已存在' : 'ID可用',
        success: true,
        data: !exists,
      };
    } catch (error) {
      return handleApiError(error, '验证分组ID');
    }
  },
};
