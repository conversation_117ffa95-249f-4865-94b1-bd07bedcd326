/*
 * @Author: <PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-07-24 17:23:31
 * @LastEditors: zhang<PERSON>an <EMAIL>
 * @LastEditTime: 2025-07-24 17:31:59
 * @Description: 页面功能描述，例如：用户列表、商品详情等
 * @FilePath: /dev-platform-ui/apps/web-ele/src/views/mcp/config/types.ts
 */
/**
 * @Description: MCP配置管理相关类型定义
 * @Author: zhangqian
 * @Date: 2025-07-24
 */

// 工具信息接口（用于显示）
export interface ToolInfo {
  id: string;
  name: string;
  displayName: string;
}

// 工具分组接口定义
export interface ToolGroup {
  id: string;
  groupId: string;
  groupName: string;
  description: string;
  toolIds: string[];
  toolCount: number;
  createdAt: string;
  tools?: ToolInfo[];
}

// 可用工具接口定义
export interface AvailableTool {
  id: string;
  name: string;
  displayName: string;
  description: string;
}

// 创建分组表单数据
export interface CreateGroupForm {
  groupName: string;
  groupId: string;
  description: string;
  selectedToolIds: string[];
}

// 表格操作类型
export type TableAction = 'copy' | 'delete' | 'edit';

// 分页信息
export interface PaginationInfo {
  current: number;
  pageSize: number;
  total: number;
}

// API响应基础结构
export interface ApiResponse<T = any> {
  code: number;
  message: string;
  data: T;
  success: boolean;
}

// 工具分组列表响应
export type ToolGroupListResponse = ApiResponse<{
  items: ToolGroup[];
  total: number;
}>;

// 可用工具列表响应
export type AvailableToolListResponse = ApiResponse<{
  items: AvailableTool[];
  total: number;
}>;
