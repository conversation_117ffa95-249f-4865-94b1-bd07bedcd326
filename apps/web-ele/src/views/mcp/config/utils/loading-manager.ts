/*
 * @Author: <PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-07-24 17:45:00
 * @LastEditors: zhangqian <EMAIL>
 * @LastEditTime: 2025-07-24 18:16:29
 * @Description: 加载状态管理工具
 * @FilePath: /dev-platform-ui/apps/web-ele/src/views/mcp/config/utils/loading-manager.ts
 */

import type { Ref } from 'vue';
import { computed, ref } from 'vue';

import { ElLoading } from 'element-plus';

import { LOADING_STATES } from '../constants';

// 加载状态类型
export type LoadingState = (typeof LOADING_STATES)[keyof typeof LOADING_STATES];

// 加载管理器选项
export interface LoadingManagerOptions {
  initialState?: LoadingState;
  autoReset?: boolean;
  resetDelay?: number;
}

// 加载管理器类
export class LoadingManager {
  get data() {
    return this._data;
  }
  get error() {
    return this._error;
  }
  get isError() {
    return computed(() => this._state.value === LOADING_STATES.ERROR);
  }
  get isIdle() {
    return computed(() => this._state.value === LOADING_STATES.IDLE);
  }
  get isLoading() {
    return computed(() => this._state.value === LOADING_STATES.LOADING);
  }

  get isSuccess() {
    return computed(() => this._state.value === LOADING_STATES.SUCCESS);
  }

  // 计算属性
  get state() {
    return this._state;
  }

  private _data: Ref<any>;

  private _error: Ref<Error | null>;

  private _options: LoadingManagerOptions;

  private _resetTimer: NodeJS.Timeout | null = null;

  private _state: Ref<LoadingState>;

  constructor(options: LoadingManagerOptions = {}) {
    this._options = {
      initialState: LOADING_STATES.IDLE,
      autoReset: false,
      resetDelay: 3000,
      ...options,
    };

    this._state = ref(this._options.initialState!);
    this._error = ref(null);
    this._data = ref(null);
  }

  // 销毁管理器
  destroy() {
    this._clearResetTimer();
  }

  // 执行异步操作
  async execute<T>(
    asyncFn: () => Promise<T>,
    onSuccess?: (data: T) => void,
    onError?: (error: Error) => void,
  ): Promise<null | T> {
    this.setLoading();

    try {
      const result = await asyncFn();
      this.setSuccess(result);

      if (onSuccess) {
        onSuccess(result);
      }

      return result;
    } catch (error) {
      const err = error instanceof Error ? error : new Error(String(error));
      this.setError(err);

      if (onError) {
        onError(err);
      }

      return null;
    }
  }

  // 重置状态
  reset() {
    this._state.value = this._options.initialState!;
    this._error.value = null;
    this._data.value = null;
    this._clearResetTimer();
  }

  // 设置错误状态
  setError(error: Error | string) {
    this._state.value = LOADING_STATES.ERROR;
    this._error.value = typeof error === 'string' ? new Error(error) : error;
    this._scheduleReset();
  }

  // 设置加载状态
  setLoading() {
    this._state.value = LOADING_STATES.LOADING;
    this._error.value = null;
    this._clearResetTimer();
  }

  // 设置成功状态
  setSuccess(data?: any) {
    this._state.value = LOADING_STATES.SUCCESS;
    this._error.value = null;
    if (data !== undefined) {
      this._data.value = data;
    }
    this._scheduleReset();
  }

  // 私有方法：清除重置定时器
  private _clearResetTimer() {
    if (this._resetTimer) {
      clearTimeout(this._resetTimer);
      this._resetTimer = null;
    }
  }

  // 私有方法：安排重置
  private _scheduleReset() {
    if (this._options.autoReset) {
      this._clearResetTimer();
      this._resetTimer = setTimeout(() => {
        this.reset();
      }, this._options.resetDelay);
    }
  }
}

// 创建加载管理器的组合式函数
export function useLoadingManager(options?: LoadingManagerOptions) {
  const manager = new LoadingManager(options);

  return {
    state: manager.state,
    error: manager.error,
    data: manager.data,
    isLoading: manager.isLoading,
    isSuccess: manager.isSuccess,
    isError: manager.isError,
    isIdle: manager.isIdle,
    setLoading: () => manager.setLoading(),
    setSuccess: (data?: any) => manager.setSuccess(data),
    setError: (error: Error | string) => manager.setError(error),
    reset: () => manager.reset(),
    execute: <T>(
      asyncFn: () => Promise<T>,
      onSuccess?: (data: T) => void,
      onError?: (error: Error) => void,
    ) => manager.execute(asyncFn, onSuccess, onError),
    destroy: () => manager.destroy(),
  };
}

// 全局加载状态管理
class GlobalLoadingManager {
  get isGlobalLoading() {
    return computed(() => this._loadingCount.value > 0);
  }
  private _loadingCount = ref(0);

  private _loadingInstances: Map<string, any> = new Map();

  // 隐藏全局加载
  hideGlobal(id?: string) {
    if (id && this._loadingInstances.has(id)) {
      const instance = this._loadingInstances.get(id);
      instance.close();
      this._loadingInstances.delete(id);
      this._loadingCount.value--;
    } else {
      // 隐藏所有加载实例
      this._loadingInstances.forEach((instance) => {
        instance.close();
      });
      this._loadingInstances.clear();
      this._loadingCount.value = 0;
    }
  }

  // 显示全局加载
  showGlobal(
    options: {
      background?: string;
      target?: HTMLElement | string;
      text?: string;
    } = {},
  ): string {
    const id = `loading_${Date.now()}_${Math.random().toString(36).slice(2, 11)}`;

    const loadingInstance = ElLoading.service({
      text: options.text || '加载中...',
      target: options.target,
      background: options.background || 'rgba(0, 0, 0, 0.7)',
      customClass: 'mcp-global-loading',
    });

    this._loadingInstances.set(id, loadingInstance);
    this._loadingCount.value++;

    return id;
  }

  // 自动管理的全局加载
  async withGlobalLoading<T>(
    asyncFn: () => Promise<T>,
    options?: {
      background?: string;
      target?: HTMLElement | string;
      text?: string;
    },
  ): Promise<T> {
    const loadingId = this.showGlobal(options);

    try {
      const result = await asyncFn();
      return result;
    } finally {
      this.hideGlobal(loadingId);
    }
  }
}

// 全局加载管理器实例
export const globalLoadingManager = new GlobalLoadingManager();

// 全局加载管理器的组合式函数
export function useGlobalLoading() {
  return {
    isGlobalLoading: globalLoadingManager.isGlobalLoading,
    showGlobal: (
      options?: Parameters<typeof globalLoadingManager.showGlobal>[0],
    ) => globalLoadingManager.showGlobal(options),
    hideGlobal: (id?: string) => globalLoadingManager.hideGlobal(id),
    withGlobalLoading: <T>(
      asyncFn: () => Promise<T>,
      options?: Parameters<typeof globalLoadingManager.withGlobalLoading>[1],
    ) => globalLoadingManager.withGlobalLoading(asyncFn, options),
  };
}

// 骨架屏加载管理
export function useSkeletonLoading(itemCount: number = 5) {
  const isLoading = ref(true);
  const skeletonItems = computed(() =>
    Array.from({ length: itemCount }, (_, i) => i),
  );

  const startLoading = () => {
    isLoading.value = true;
  };

  const stopLoading = () => {
    isLoading.value = false;
  };

  return {
    isLoading,
    skeletonItems,
    startLoading,
    stopLoading,
  };
}

// 按钮加载状态管理
export function useButtonLoading() {
  const loadingStates = ref<Record<string, boolean>>({});

  const setButtonLoading = (buttonId: string, loading: boolean) => {
    loadingStates.value[buttonId] = loading;
  };

  const isButtonLoading = (buttonId: string) => {
    return computed(() => !!loadingStates.value[buttonId]);
  };

  const withButtonLoading = async <T>(
    buttonId: string,
    asyncFn: () => Promise<T>,
  ): Promise<T> => {
    setButtonLoading(buttonId, true);
    try {
      const result = await asyncFn();
      return result;
    } finally {
      setButtonLoading(buttonId, false);
    }
  };

  return {
    loadingStates,
    setButtonLoading,
    isButtonLoading,
    withButtonLoading,
  };
}
