/*
 * @Author: <PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-07-24 17:45:00
 * @LastEditors: zhang<PERSON>an <EMAIL>
 * @LastEditTime: 2025-07-25 10:43:20
 * @Description: 错误处理工具函数
 * @FilePath: /dev-platform-ui/apps/web-ele/src/views/mcp/config/utils/error-handler.ts
 */

import { ElMessage, ElNotification } from 'element-plus';

import { ERROR_TYPES } from '../constants';

// 错误信息接口
export interface ErrorInfo {
  code?: string;
  message?: string;
  details?: any;
  timestamp?: number;
}

// 错误处理选项
export interface ErrorHandlerOptions {
  showMessage?: boolean;
  showNotification?: boolean;
  logToConsole?: boolean;
  operation?: string;
  fallbackMessage?: string;
}

// 默认错误处理选项
const defaultOptions: ErrorHandlerOptions = {
  showMessage: true,
  showNotification: false,
  logToConsole: true,
  operation: '操作',
  fallbackMessage: '操作失败，请稍后重试',
};

// 错误类型到用户友好消息的映射
const errorMessageMap: Record<string, string> = {
  [ERROR_TYPES.NETWORK_ERROR]: '网络连接失败，请检查网络后重试',
  [ERROR_TYPES.VALIDATION_ERROR]: '数据验证失败，请检查输入内容',
  [ERROR_TYPES.PERMISSION_ERROR]: '权限不足，请联系管理员',
  [ERROR_TYPES.SERVER_ERROR]: '服务器内部错误，请稍后重试',
};

// 主要错误处理函数
export function handleError(
  error: any,
  options: ErrorHandlerOptions = {},
): void {
  const opts = { ...defaultOptions, ...options };

  // 解析错误信息
  const errorInfo = parseError(error);

  // 生成用户友好的错误消息
  const userMessage = generateUserMessage(errorInfo, opts);

  // 记录错误日志
  if (opts.logToConsole) {
    logError(errorInfo, opts.operation || defaultOptions.operation || '操作');
  }

  // 显示错误消息
  if (opts.showMessage) {
    ElMessage.error(userMessage);
  }

  // 显示通知
  if (opts.showNotification) {
    ElNotification.error({
      title: `${opts.operation}失败`,
      message: userMessage,
      duration: 5000,
    });
  }
}

// 解析错误对象
function parseError(error: any): ErrorInfo {
  const errorInfo: ErrorInfo = {
    timestamp: Date.now(),
  };

  if (typeof error === 'string') {
    errorInfo.message = error;
  } else if (error instanceof Error) {
    errorInfo.message = error.message;
    errorInfo.details = {
      name: error.name,
      stack: error.stack,
    };
  } else if (error && typeof error === 'object') {
    errorInfo.code = error.code;
    errorInfo.message = error.message;
    errorInfo.details = error;
  } else {
    errorInfo.message = '未知错误';
    errorInfo.details = error;
  }

  return errorInfo;
}

// 生成用户友好的错误消息
function generateUserMessage(
  errorInfo: ErrorInfo,
  options: ErrorHandlerOptions,
): string {
  // 如果有错误码，尝试使用映射的消息
  if (errorInfo.code) {
    const mappedMessage = errorMessageMap[errorInfo.code];
    if (mappedMessage) {
      return mappedMessage;
    }
  }

  // 如果有具体的错误消息，使用它
  if (errorInfo.message && isUserFriendlyMessage(errorInfo.message)) {
    return errorInfo.message;
  }

  // 使用后备消息
  return (
    options.fallbackMessage ||
    defaultOptions.fallbackMessage ||
    '操作失败，请稍后重试'
  );
}

// 判断消息是否对用户友好
function isUserFriendlyMessage(message: string): boolean {
  // 过滤掉技术性错误消息
  const technicalKeywords = [
    'undefined',
    'null',
    'TypeError',
    'ReferenceError',
    'SyntaxError',
    'fetch',
    'XMLHttpRequest',
    'Promise',
    'async',
    'await',
  ];

  const lowerMessage = message.toLowerCase();
  return !technicalKeywords.some((keyword) =>
    lowerMessage.includes(keyword.toLowerCase()),
  );
}

// 记录错误日志
function logError(errorInfo: ErrorInfo, operation: string): void {
  const logData = {
    operation,
    timestamp: new Date(errorInfo.timestamp || Date.now()).toISOString(),
    error: errorInfo,
  };

  console.error(`🚨 ${operation}失败`);
  console.error('错误详情:', logData);

  if (errorInfo.details?.stack) {
    console.error('错误堆栈:', errorInfo.details.stack);
  }
}

// 网络错误处理
export function handleNetworkError(
  error: any,
  operation: string = '网络请求',
): void {
  handleError(error, {
    operation,
    fallbackMessage: '网络连接失败，请检查网络设置后重试',
    showNotification: true,
  });
}

// 验证错误处理
export function handleValidationError(
  error: any,
  operation: string = '数据验证',
): void {
  handleError(error, {
    operation,
    fallbackMessage: '输入数据不符合要求，请检查后重试',
    showMessage: true,
  });
}

// 权限错误处理
export function handlePermissionError(
  error: any,
  operation: string = '权限验证',
): void {
  handleError(error, {
    operation,
    fallbackMessage: '权限不足，请联系管理员获取相应权限',
    showNotification: true,
  });
}

// 服务器错误处理
export function handleServerError(
  error: any,
  operation: string = '服务器请求',
): void {
  handleError(error, {
    operation,
    fallbackMessage: '服务器暂时无法处理请求，请稍后重试',
    showNotification: true,
  });
}

// 创建错误处理装饰器
export function withErrorHandler(
  operation: string,
  options?: Partial<ErrorHandlerOptions>,
) {
  return function (
    target: any,
    propertyKey: string,
    descriptor: PropertyDescriptor,
  ) {
    console.warn({ target, propertyKey });
    const originalMethod = descriptor.value;

    descriptor.value = async function (...args: any[]) {
      try {
        return await originalMethod.apply(this, args);
      } catch (error) {
        handleError(error, {
          operation,
          ...options,
        });
        throw error;
      }
    };

    return descriptor;
  };
}

// 创建异步错误处理包装器
export function wrapWithErrorHandler<
  T extends (...args: any[]) => Promise<any>,
>(fn: T, operation: string, options?: Partial<ErrorHandlerOptions>): T {
  return (async (...args: any[]) => {
    try {
      return await fn(...args);
    } catch (error) {
      handleError(error, {
        operation,
        ...options,
      });
      throw error;
    }
  }) as T;
}
