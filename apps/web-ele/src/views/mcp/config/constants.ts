/*
 * @Author: <PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-07-24 17:45:00
 * @LastEditors: z<PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-07-24 17:45:00
 * @Description: MCP配置管理常量定义
 * @FilePath: /dev-platform-ui/apps/web-ele/src/views/mcp/config/constants.ts
 */

// 工具分组相关常量
export const TOOL_GROUP_PREFIX = 'TG_CUSTOM_';
export const DEFAULT_PAGE_SIZE = 10;
export const MAX_GROUP_NAME_LENGTH = 50;
export const MAX_DESCRIPTION_LENGTH = 200;
export const MIN_GROUP_NAME_LENGTH = 2;

// 表格配置常量
export const TABLE_MIN_HEIGHT = 400;
export const TABLE_MAX_HEIGHT = 500;
export const DIALOG_WIDTH = '80%';

// 分页配置
export const PAGE_SIZES = [10, 20, 50];
export const DEFAULT_CURRENT_PAGE = 1;

// 验证消息常量
export const VALIDATION_MESSAGES = {
  REQUIRED_GROUP_NAME: '请输入分组名称',
  REQUIRED_TOOLS: '请至少选择一个工具',
  INVALID_GROUP_NAME: '分组名称格式不正确',
  GROUP_NAME_TOO_SHORT: `分组名称至少需要${MIN_GROUP_NAME_LENGTH}个字符`,
  GROUP_NAME_TOO_LONG: `分组名称不能超过${MAX_GROUP_NAME_LENGTH}个字符`,
  DESCRIPTION_TOO_LONG: `描述不能超过${MAX_DESCRIPTION_LENGTH}个字符`,
  INVALID_GROUP_NAME_PATTERN: '只能包含中文、英文、数字、下划线和横线',
} as const;

// 操作消息常量
export const OPERATION_MESSAGES = {
  CREATE_SUCCESS: '分组创建成功',
  CREATE_FAILED: '创建失败，请稍后重试',
  DELETE_SUCCESS: '删除成功',
  DELETE_FAILED: '删除失败，请稍后重试',
  UPDATE_SUCCESS: '更新成功',
  UPDATE_FAILED: '更新失败，请稍后重试',
  COPY_SUCCESS: '已复制分组ID',
  COPY_FAILED: '复制失败',
  ID_GENERATE_SUCCESS: 'ID生成成功',
  ID_GENERATE_FAILED: 'ID生成失败，请稍后重试',
  TOOL_SELECTION_SUCCESS: '工具选择成功',
  TOOL_REMOVE_SUCCESS: '工具移除成功',
} as const;

// 错误类型常量
export const ERROR_TYPES = {
  NETWORK_ERROR: 'NETWORK_ERROR',
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  PERMISSION_ERROR: 'PERMISSION_ERROR',
  SERVER_ERROR: 'SERVER_ERROR',
} as const;

// 加载状态常量
export const LOADING_STATES = {
  IDLE: 'idle',
  LOADING: 'loading',
  SUCCESS: 'success',
  ERROR: 'error',
} as const;

// 表单字段名常量
export const FORM_FIELDS = {
  GROUP_NAME: 'groupName',
  GROUP_ID: 'groupId',
  DESCRIPTION: 'description',
  SELECTED_TOOL_IDS: 'selectedToolIds',
  TOOL_NAME: 'toolName',
} as const;

// 表格列字段常量
export const TABLE_COLUMNS = {
  GROUP_ID: 'groupId',
  GROUP_NAME: 'groupName',
  TOOLS: 'tools',
  TOOL_COUNT: 'toolCount',
  CREATED_AT: 'createdAt',
  ACTION: 'action',
} as const;

// 正则表达式常量
export const REGEX_PATTERNS = {
  GROUP_NAME: /^[\u4e00-\u9fa5a-zA-Z0-9_-]+$/,
  GROUP_ID: /^TG_[A-Z_]+_\d+$/,
} as const;

// 延迟时间常量（毫秒）
export const DELAYS = {
  DEBOUNCE_SEARCH: 300,
  TOOLTIP_SHOW: 300,
  API_SIMULATION: 1000,
  ID_GENERATION: 500,
  TABLE_SELECTION_DELAY: 200,
} as const;
