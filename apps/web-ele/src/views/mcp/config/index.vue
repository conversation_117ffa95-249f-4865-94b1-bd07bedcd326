<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-07-14 15:11:40
 * @LastEditors: zhangqian <EMAIL>
 * @LastEditTime: 2025-07-25 11:02:50
 * @Description: mcp管理中心-配置管理-应用授权页面（重构版）
 * @FilePath: /dev-platform-ui/apps/web-ele/src/views/mcp/config/index.vue
-->
<script lang="ts" setup>
// 临时解决方案：直接定义类型
import { computed, h, onMounted, watch } from 'vue';

import { Page } from '@vben/common-ui';

import { Delete, InfoFilled, Plus } from '@element-plus/icons-vue';
import { ElButton, ElCard, ElMessage, ElTag, ElTooltip } from 'element-plus';

import { useVbenForm } from '#/adapter/form';
import { useVbenVxeGrid } from '#/adapter/vxe-table';

import AgentIntegrationGuide from './components/AgentIntegrationGuide.vue';
import ChooseToolDialog from './components/ChooseToolDialog.vue';
import { useFormValidation } from './composables/useFormValidation';
// 导入组合式函数
import { useToolGroups } from './composables/useToolGroups';
import { useToolSelection } from './composables/useToolSelection';
// 导入配置
import { createSearchFormOptions } from './config/form-config';
import { createToolGroupGridOptions } from './config/grid-config';

interface ToolInfo {
  id: string;
  name: string;
  displayName: string;
}

interface ToolGroup {
  id: string;
  groupId: string;
  groupName: string;
  description: string;
  toolIds: string[];
  toolCount: number;
  createdAt: string;
  tools: ToolInfo[];
}

// 使用组合式函数
const {
  state: toolGroupState,
  showCreateForm,
  editingGroup,
  isEditing,
  formTitle,
  searchToolGroups,
  resetSearch,
  generateGroupId,
  createToolGroup,
  updateToolGroup,
  deleteToolGroup,
  showCreateGroupForm,
  showEditGroupForm,
  hideForm,
  copyGroupId,
  gridRef,
} = useToolGroups();

const {
  dialogVisible,
  selectedCount,
  selectedTools,
  isLoading: toolSelectionLoading,
  openToolSelectionDialog,
  confirmToolSelection,
  cancelToolSelection,
  removeSelectedTool,
  setSelectedTools,
  setSelectedToolsData,
} = useToolSelection();

const { formData, isFormValid, validateFormData, resetForm, setFormData } =
  useFormValidation();

// 1.配置 分组搜索表单和分组表格
const formOptions = createSearchFormOptions(
  (values) => searchToolGroups(values),
  () => resetSearch(),
);

const gridOptions = computed(() => createToolGroupGridOptions());
const [Grid, GridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions: gridOptions.value,
});

// 设置表格引用
gridRef.value = GridApi;

// 2.创建/编辑分组表单
const editFormOptions = {
  layout: 'vertical' as const,
  schema: [
    {
      component: 'Input',
      componentProps: {
        placeholder: '例如:智能客服工具',
        clearable: true,
      },
      fieldName: 'groupName',
      label: '分组名称',
    },
    {
      component: 'Input',
      fieldName: 'groupId',
      label: '分组ID(自动生成)',
      componentProps: {
        readonly: true,
      },
    },
    {
      component: 'Input',
      componentProps: {
        placeholder: '请选择工具',
        clearable: true,
        readonly: true,
      },
      fieldName: 'selectedToolIds',
      label: '选择工具',
    },
    {
      component: 'Input',
      componentProps: {
        placeholder: '描述此分组的用途...',
        clearable: true,
        type: 'textarea',
        rows: 5,
        maxlength: 200,
        showWordLimit: true,
      },
      fieldName: 'description',
      label: '分组描述',
    },
  ],
};
const [Form] = useVbenForm(editFormOptions);

// 处理表单提交
const handleFormSubmit = async () => {
  if (!validateFormData()) {
    ElMessage.error('请检查表单输入');
    return;
  }

  const success = isEditing.value
    ? await updateToolGroup(editingGroup.value!.id, formData)
    : await createToolGroup(formData);

  if (success) {
    hideForm();
    resetForm();
  }
};

// 处理弹窗工具选择确认
const handleToolSelectionConfirm = (selectedTools: any[]) => {
  setSelectedToolsData(selectedTools); // 保存完整的工具数据
  formData.selectedToolIds = selectedTools.map((tool) => tool.id);
  confirmToolSelection(selectedTools.length); // 传入选中的工具数量
};

// 处理弹窗工具选择取消
const handleToolSelectionCancel = () => {
  cancelToolSelection();
};

// 重新生成分组ID
const regenerateGroupId = async () => {
  const newId = await generateGroupId();
  formData.groupId = newId;
  setFormData({ groupId: newId });
};

// 编辑分组
const editGroup = (group: ToolGroup) => {
  setFormData({
    groupName: group.groupName,
    groupId: group.groupId,
    description: group.description,
    selectedToolIds: group.toolIds,
  });
  // 使用完整的工具数据
  if (group.tools && group.tools.length > 0) {
    // 将 ToolInfo 转换为 AvailableTool 格式
    const availableTools = group.tools.map((tool) => ({
      ...tool,
      description: tool.displayName || '', // 添加缺失的 description 字段
    }));
    setSelectedToolsData(availableTools);
  } else {
    setSelectedTools(group.toolIds);
  }
  showEditGroupForm(group);
};

// 初始化
onMounted(async () => {
  try {
    // 表格数据现在由 proxyConfig 自动加载，无需手动调用 loadToolGroups
    const newId = await generateGroupId();
    if (formData && newId) {
      formData.groupId = newId;
    }
  } catch (error) {
    console.error('初始化失败:', error);
  }
});

// 监听编辑状态变化，同步表单数据
watch(editingGroup, (group) => {
  if (group && setFormData && setSelectedToolsData) {
    setFormData({
      groupName: group.groupName || '',
      groupId: group.groupId || '',
      description: group.description || '',
      selectedToolIds: group.toolIds || [],
    });
    // 使用完整的工具数据
    if (group.tools && group.tools.length > 0) {
      // 将 ToolInfo 转换为 AvailableTool 格式
      const availableTools = group.tools.map((tool) => ({
        ...tool,
        description: tool.displayName || '', // 添加缺失的 description 字段
      }));
      setSelectedToolsData(availableTools);
    } else {
      setSelectedTools(group.toolIds || []);
    }
  } else if (resetForm) {
    resetForm();
  }
});

// 监听表单数据变化，同步到工具选择
watch(
  () => formData.selectedToolIds,
  (newIds) => {
    setSelectedTools(newIds);
  },
);
</script>

<template>
  <Page
    title="应用授权"
    description="将相关的API工具组织成逻辑分组，便于管理和授权"
  >
    <div class="p-4 md:p-6 lg:p-8">
      <!-- 分组列表卡片 -->
      <ElCard class="mb-6">
        <template #header>
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-medium text-gray-900">分组列表</h3>
            <ElButton
              type="primary"
              @click="showCreateGroupForm"
              class="bg-blue-600 hover:bg-blue-700"
              :loading="toolGroupState.loading"
            >
              创建分组
            </ElButton>
          </div>
        </template>

        <Grid>
          <template #groupId="{ row }">
            <span
              class="inline-block rounded bg-gray-900 px-3 py-1 font-mono text-sm text-green-400"
              role="code"
              :aria-label="`分组ID: ${row.groupId}`"
            >
              {{ row.groupId }}
            </span>
          </template>

          <!-- 包含工具插槽 -->
          <template #tools="{ row }">
            <ElTooltip
              v-if="row.tools && row.tools.length > 3"
              placement="top"
              :show-after="300"
            >
              <template #content>
                <component
                  :is="
                    () =>
                      h('div', { class: 'max-w-xs' }, [
                        h(
                          'div',
                          { class: 'font-medium mb-2 text-sm' },
                          `全部工具 (${row.tools.length}个):`,
                        ),
                        h(
                          'div',
                          { class: 'flex flex-wrap gap-1' },
                          row.tools.map(
                            (tool: {
                              displayName: string;
                              id: string;
                              name: string;
                            }) =>
                              h(
                                ElTag,
                                {
                                  key: tool.id,
                                  size: 'small',
                                  class: 'mb-1',
                                },
                                () => tool.name,
                              ),
                          ),
                        ),
                      ])
                  "
                />
              </template>
              <div class="flex cursor-pointer flex-wrap gap-1">
                <ElTag
                  v-for="tool in row.tools?.slice(0, 3)"
                  :key="tool.id"
                  size="small"
                  class="mb-1 mr-1"
                >
                  {{ tool.name }}
                </ElTag>
                <span class="text-sm text-gray-500">
                  +{{ row.tools.length - 3 }}
                </span>
              </div>
            </ElTooltip>
            <div
              v-else-if="row.tools && row.tools.length > 0"
              class="flex flex-wrap gap-1"
            >
              <ElTag
                v-for="tool in row.tools"
                :key="tool.id"
                size="small"
                class="mb-1 mr-1"
              >
                {{ tool.name }}
              </ElTag>
            </div>
            <div v-else class="text-sm text-gray-400">无工具</div>
          </template>

          <!-- 工具数量插槽 -->
          <template #toolCount="{ row }">
            <span class="font-medium text-blue-600">
              {{ row.toolCount }} 个工具
            </span>
          </template>

          <!-- 操作插槽 -->
          <template #action="{ row }">
            <div class="flex gap-2">
              <ElButton
                size="small"
                @click="copyGroupId(row.groupId)"
                class="text-blue-600 hover:text-blue-800"
                link
                :aria-label="`复制分组ID: ${row.groupId}`"
              >
                复制ID
              </ElButton>
              <ElButton
                size="small"
                type="primary"
                @click="editGroup(row)"
                link
                :aria-label="`编辑分组: ${row.groupName}`"
              >
                编辑
              </ElButton>
              <ElButton
                size="small"
                type="danger"
                @click="deleteToolGroup(row)"
                link
                :loading="toolGroupState.deleteLoading"
                :aria-label="`删除分组: ${row.groupName}`"
              >
                删除
              </ElButton>
            </div>
          </template>
        </Grid>
      </ElCard>

      <!-- 创建/编辑 分组表单 -->
      <ElCard v-show="showCreateForm" class="mb-6">
        <template #header>
          <h3 class="text-lg font-medium text-gray-900">{{ formTitle }}</h3>
        </template>
        <Form>
          <!-- 分组ID -->
          <template #groupId>
            <div>
              <div class="flex items-center gap-4">
                <span
                  class="inline-block rounded bg-gray-900 px-3 py-2 font-mono text-sm text-green-400"
                  role="code"
                  :aria-label="`分组ID: ${formData.groupId}`"
                >
                  {{ formData.groupId }}
                </span>
                <ElButton
                  size="small"
                  @click="regenerateGroupId"
                  :loading="toolGroupState.generateIdLoading"
                  class="text-gray-600 hover:text-gray-800"
                  :disabled="isEditing"
                >
                  重新生成
                </ElButton>
              </div>
              <p class="mt-1 text-xs text-gray-500">
                格式：TG_[业务领域]_[序号]，此ID将在Agent配置中使用
              </p>
            </div>
          </template>

          <!-- 选择工具 -->
          <template #selectedToolIds>
            <div>
              <!-- 选择工具按钮 -->
              <div class="mb-4">
                <ElButton
                  type="primary"
                  :icon="Plus"
                  @click="openToolSelectionDialog"
                  :loading="toolSelectionLoading"
                >
                  选择工具
                </ElButton>
                <span
                  v-if="selectedCount > 0"
                  class="ml-2 text-sm text-gray-500"
                >
                  已选择 {{ selectedCount }} 个工具
                </span>
              </div>

              <!-- 已选择的工具表格 -->
              <div v-if="selectedCount > 0" class="selected-tools-table mb-6">
                <h4 class="mb-3 text-base font-medium">已选择的工具</h4>
                <el-table :data="selectedTools" border stripe>
                  <el-table-column
                    prop="name"
                    label="工具名称"
                    min-width="150"
                  />
                  <el-table-column
                    prop="displayName"
                    label="显示名称"
                    min-width="150"
                  />
                  <el-table-column
                    prop="description"
                    label="描述"
                    min-width="250"
                    show-overflow-tooltip
                  />
                  <el-table-column label="操作" width="80" align="center">
                    <template #default="{ row }">
                      <ElButton
                        type="danger"
                        :icon="Delete"
                        size="small"
                        circle
                        @click="removeSelectedTool(row)"
                        :title="`删除工具: ${row.displayName}`"
                        :aria-label="`删除工具: ${row.displayName}`"
                      />
                    </template>
                  </el-table-column>
                </el-table>
              </div>

              <!-- 空状态 -->
              <div v-else class="empty-state">
                <div class="flex h-64 flex-col items-center justify-center">
                  <div class="mb-4 text-6xl text-gray-400" aria-hidden="true">
                    <InfoFilled />
                  </div>
                  <div class="mb-2 text-lg font-medium text-gray-500">
                    暂未选择工具
                  </div>
                  <div class="mb-4 text-sm text-gray-400">
                    点击"选择工具"按钮开始选择要添加的工具
                  </div>
                  <ElButton
                    type="primary"
                    :icon="Plus"
                    @click="openToolSelectionDialog"
                    :loading="toolSelectionLoading"
                  >
                    选择工具
                  </ElButton>
                </div>
              </div>
            </div>
          </template>

          <!-- 操作按钮 -->
          <div class="flex gap-3 pt-4">
            <ElButton @click="hideForm">取消</ElButton>
            <ElButton
              type="primary"
              @click="handleFormSubmit"
              :loading="toolGroupState.createLoading"
              class="bg-green-600 hover:bg-green-700"
              :disabled="!isFormValid"
            >
              {{ isEditing ? '更新分组' : '创建分组' }}
            </ElButton>
          </div>
        </Form>
      </ElCard>

      <!-- 选择工具对话框 -->
      <ChooseToolDialog
        v-model:visible="dialogVisible"
        :selected-tool-ids="formData.selectedToolIds"
        :selected-tools-data="selectedTools"
        @confirm="handleToolSelectionConfirm"
        @cancel="handleToolSelectionCancel"
      />

      <!-- Agent集成指南组件 -->
      <AgentIntegrationGuide
        :group-id="formData.groupId"
        :group-name="formData.groupName"
        :selected-tool-ids="formData.selectedToolIds"
      />
    </div>
  </Page>
</template>

<style scoped>
.selected-tools-table {
  background: #f8fafc;
  border-radius: 8px;
  padding: 16px;
  border: 1px solid #e2e8f0;
}

.empty-state {
  background: #f8fafc;
  border-radius: 8px;
  border: 2px dashed #cbd5e0;
  margin: 16px 0;
}

.dialog-content {
  padding: 0;
}

.dialog-footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

/* 对话框样式优化 */
:deep(.el-dialog) {
  border-radius: 12px;
}

:deep(.el-dialog__header) {
  padding: 20px 24px 16px;
  border-bottom: 1px solid #e5e7eb;
}

:deep(.el-dialog__body) {
  padding: 20px 24px;
}

:deep(.el-dialog__footer) {
  padding: 16px 24px 20px;
  border-top: 1px solid #e5e7eb;
}

/* 表格样式优化 */
:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.el-table th) {
  background-color: #f1f5f9;
  color: #374151;
  font-weight: 600;
}

:deep(.el-table td) {
  border-bottom: 1px solid #f1f5f9;
}

:deep(.el-table tr:hover > td) {
  background-color: #f8fafc;
}
</style>
