<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-07-14 15:11:40
 * @LastEditors: zhangqian <EMAIL>
 * @LastEditTime: 2025-07-23 11:03:10
 * @Description: mcp管理中心-工具管理-添加API工具页面 （暂不使用-目前使用）
 * @FilePath: /dev-platform-ui/apps/web-ele/src/views/mcp/tool/add.vue
-->
<script lang="ts" setup>
import { computed, onMounted, reactive, ref, watch } from 'vue';

import { ElMessage } from 'element-plus';

// 当前步骤
const currentStep = ref(0);

// API端点接口定义
interface ApiEndpoint {
  id: string;
  method: string;
  path: string;
  description: string;
}

interface ApiGroup {
  groupName: string;
  endpoints: ApiEndpoint[];
}

// 步骤1：API端点选择相关状态
const loading = ref(false);
const error = ref(false);
const searchKeyword = ref('');
const selectedApiIds = ref<string[]>([]);

// Mock API数据 - 实际项目中应从后端获取
const apiGroups = ref<ApiGroup[]>([
  {
    groupName: '订单管理API',
    endpoints: [
      {
        id: 'ep_order_list',
        method: 'GET',
        path: '/api/v2/orders',
        description: '获取订单列表',
      },
      {
        id: 'ep_order_detail',
        method: 'GET',
        path: '/api/v2/orders/{orderId}',
        description: '获取订单详情',
      },
      {
        id: 'ep_order_create',
        method: 'POST',
        path: '/api/v2/orders',
        description: '创建新订单',
      },
      {
        id: 'ep_order_update_status',
        method: 'PUT',
        path: '/api/v2/orders/{orderId}/status',
        description: '更新订单状态',
      },
      {
        id: 'ep_order_cancel',
        method: 'DELETE',
        path: '/api/v2/orders/{orderId}',
        description: '取消订单',
      },
    ],
  },
  {
    groupName: '商品管理API',
    endpoints: [
      {
        id: 'ep_product_search',
        method: 'GET',
        path: '/api/v2/products/search',
        description: '搜索商品',
      },
      {
        id: 'ep_product_detail',
        method: 'GET',
        path: '/api/v2/products/{productId}',
        description: '获取商品详情',
      },
      {
        id: 'ep_product_inventory',
        method: 'GET',
        path: '/api/v2/products/{productId}/inventory',
        description: '查询库存',
      },
    ],
  },
]);

// 获取API端点数据
const fetchApiEndpoints = async () => {
  loading.value = true;
  error.value = false;

  try {
    // 模拟API调用
    await new Promise((resolve) => setTimeout(resolve, 1000));
    // 实际项目中应该调用: const response = await api.get('/api/v1/endpoint-definitions');
    // apiGroups.value = response.data;
  } catch {
    error.value = true;
    ElMessage.error('获取API端点失败，请重试');
  } finally {
    loading.value = false;
  }
};

// 页面加载时获取数据
onMounted(() => {
  fetchApiEndpoints();
});

// 搜索过滤
const filteredApiGroups = computed(() => {
  if (!searchKeyword.value.trim()) return apiGroups.value;

  const keyword = searchKeyword.value.trim().toLowerCase();
  return apiGroups.value
    .map((group) => ({
      ...group,
      endpoints: group.endpoints.filter(
        (endpoint) =>
          endpoint.path.toLowerCase().includes(keyword) ||
          endpoint.description.toLowerCase().includes(keyword) ||
          endpoint.method.toLowerCase().includes(keyword),
      ),
    }))
    .filter((group) => group.endpoints.length > 0);
});

// 分组选择逻辑
const isGroupAllSelected = (group: ApiGroup) => {
  return group.endpoints.every((endpoint) =>
    selectedApiIds.value.includes(endpoint.id),
  );
};

const isGroupIndeterminate = (group: ApiGroup) => {
  const selectedCount = group.endpoints.filter((endpoint) =>
    selectedApiIds.value.includes(endpoint.id),
  ).length;
  return selectedCount > 0 && selectedCount < group.endpoints.length;
};

const handleGroupSelection = (group: ApiGroup, checked: boolean) => {
  const groupEndpointIds = group.endpoints.map((endpoint) => endpoint.id);

  if (checked) {
    // 添加该分组下所有未选中的端点
    const newIds = groupEndpointIds.filter(
      (id) => !selectedApiIds.value.includes(id),
    );
    selectedApiIds.value.push(...newIds);
  } else {
    // 移除该分组下所有已选中的端点
    selectedApiIds.value = selectedApiIds.value.filter(
      (id) => !groupEndpointIds.includes(id),
    );
  }

  // 手动触发工具配置生成
  generateToolConfigs();
};

// 单个API选择逻辑
const handleApiSelection = (apiId: string, checked: boolean) => {
  if (checked) {
    if (!selectedApiIds.value.includes(apiId)) {
      selectedApiIds.value.push(apiId);
    }
  } else {
    selectedApiIds.value = selectedApiIds.value.filter((id) => id !== apiId);
  }

  // 手动触发工具配置生成
  generateToolConfigs();
};

// 是否可以进入下一步
const canProceedToNext = computed(() => selectedApiIds.value.length > 0);

// 步骤2：工具配置
interface ToolConfig {
  apiId: string;
  method: string;
  path: string;
  description: string;
  toolName: string;
  displayName: string;
  toolDescription: string;
}

const toolConfigs = ref<ToolConfig[]>([]);

// 生成工具配置的函数
const generateToolConfigs = () => {
  const allEndpoints = apiGroups.value.flatMap((group) => group.endpoints);

  toolConfigs.value = selectedApiIds.value
    .map((id) => {
      const endpoint = allEndpoints.find((ep) => ep.id === id);
      if (!endpoint) {
        console.warn('Endpoint not found for id:', id);
        return null;
      }

      // 智能生成工具名称
      const suggestedName = generateToolName(endpoint.method, endpoint.path);

      const config = {
        apiId: id,
        method: endpoint.method,
        path: endpoint.path,
        description: endpoint.description,
        toolName: suggestedName,
        displayName: endpoint.description,
        toolDescription: `${endpoint.description}，支持通过自然语言调用该API。`,
      };

      return config;
    })
    .filter(Boolean) as ToolConfig[];
};

// 监听选中的API变化，自动生成工具配置
watch(
  () => selectedApiIds.value,
  () => {
    generateToolConfigs();
  },
  { immediate: true, deep: true },
);

watch(
  () => apiGroups.value,
  () => {
    if (selectedApiIds.value.length > 0) {
      generateToolConfigs();
    }
  },
  { immediate: true, deep: true },
);

// 智能生成工具名称
const generateToolName = (method: string, path: string): string => {
  const pathParts = path
    .split('/')
    .filter((part) => part && !part.startsWith('{'));
  const resource =
    pathParts[pathParts.length - 1] || pathParts[pathParts.length - 2];

  const methodMap: Record<string, string> = {
    GET: 'get',
    POST: 'create',
    PUT: 'update',
    DELETE: 'delete',
  };

  const verb = methodMap[method] || method.toLowerCase();

  if (resource) {
    // 转换为驼峰命名
    const camelResource = resource.replaceAll(/-([a-z])/g, (_, letter) =>
      letter.toUpperCase(),
    );
    return `${verb}${camelResource.charAt(0).toUpperCase() + camelResource.slice(1)}`;
  }

  return `${verb}Api`;
};

// 步骤3：参数转换配置
interface ParamTransformer {
  aiParam: string;
  apiParam: string;
  transformRule: 'builtin_time' | 'custom' | 'mapping' | 'none';
  mappingTable?: string;
  customFunction?: string;
  description?: string;
}

const paramTransformers = ref<Record<string, ParamTransformer[]>>({});

// 转换规则选项
const transformRuleOptions = [
  { label: '使用内置时间范围转换器', value: 'builtin_time' },
  { label: '自定义映射表', value: 'mapping' },
  { label: '自定义转换函数', value: 'custom' },
  { label: '无转换', value: 'none' },
];

// 自定义函数模板
const customFunctionTemplate = `/**
 * @param {any} ai_param - The parameter value provided by the AI.
 * @returns {object} - The object containing parameters for the API call.
 */
function transform(ai_param) {
  // Your custom logic here
  // e.g., return { api_param1: ..., api_param2: ... };
  return {};
}`;

// 监听工具配置变化，初始化参数转换器
watch(
  toolConfigs,
  (newConfigs) => {
    const newTransformers: Record<string, ParamTransformer[]> = {};

    newConfigs.forEach((tool) => {
      // 保留已有配置或创建默认配置
      newTransformers[tool.apiId] = paramTransformers.value[tool.apiId] || [
        {
          aiParam: '',
          apiParam: '',
          transformRule: 'none',
          description: '',
        },
      ];
    });

    paramTransformers.value = newTransformers;
  },
  { immediate: true },
);

// 添加参数转换器
const addParameterTransformer = (toolId: string) => {
  if (!paramTransformers.value[toolId]) {
    paramTransformers.value[toolId] = [];
  }

  paramTransformers.value[toolId].push({
    aiParam: '',
    apiParam: '',
    transformRule: 'none',
    description: '',
  });
};

// 删除参数转换器
const removeParameterTransformer = (toolId: string, index: number) => {
  if (
    paramTransformers.value[toolId] &&
    paramTransformers.value[toolId].length > 1
  ) {
    paramTransformers.value[toolId].splice(index, 1);
  }
};

// 步骤4：测试与完成
const testQuery = ref('帮我查看最近7天的待发货订单');
const testLoading = ref(false);
const testResults = ref({
  visible: false,
  success: false,
  aiUnderstanding: '',
  parameterTransform: '',
  apiResponse: '',
});

// 运行测试
const runTest = async () => {
  if (!testQuery.value.trim()) {
    ElMessage.warning('请输入测试查询');
    return;
  }

  testLoading.value = true;

  try {
    // 模拟测试过程
    await new Promise((resolve) => setTimeout(resolve, 2000));

    testResults.value = {
      visible: true,
      success: true,
      aiUnderstanding: `选择工具：listOrders\n参数解析：{\n  "timeRange": "最近7天",\n  "orderStatus": "待发货"\n}`,
      parameterTransform: `API参数：{\n  "startDate": "2024-01-09",\n  "endDate": "2024-01-15",\n  "status": "pending_shipment"\n}`,
      apiResponse: `{\n  "success": true,\n  "data": {\n    "total": 15,\n    "orders": [\n      {"id": "ORD-2024-0156", "customer": "张三", "amount": 299.00},\n      {"id": "ORD-2024-0157", "customer": "李四", "amount": 1580.00}\n    ]\n  }\n}`,
    };

    ElMessage.success('测试成功！');
  } catch {
    testResults.value.success = false;
    ElMessage.error('测试失败，请检查配置');
  } finally {
    testLoading.value = false;
  }
};

// 完成配置
const finishForm = reactive({
  toolGroup: '',
  description: '',
});

// 保存工具配置
const saveToolConfiguration = async () => {
  try {
    // 验证配置完整性
    if (toolConfigs.value.length === 0) {
      ElMessage.warning('请至少配置一个工具');
      return;
    }

    // 构建提交数据
    const _configurationData = {
      tools: toolConfigs.value,
      parameterTransformers: paramTransformers.value,
      toolGroup: finishForm.toolGroup,
      description: finishForm.description,
    };

    // 模拟API调用
    await new Promise((resolve) => setTimeout(resolve, 1000));

    ElMessage.success('工具配置保存成功！');

    // 可以在这里跳转到工具列表页面
    // router.push('/mcp/tools');
  } catch {
    ElMessage.error('保存失败，请重试');
  }
};

// 步骤导航
const steps = [
  { title: '选择API', description: '从已有API列表中选择需要转换的端点' },
  { title: '配置工具', description: '为API设置AI友好的调用名称和描述' },
  { title: '参数转换', description: '配置自然语言参数到API参数的映射' },
  { title: '测试与完成', description: '测试工具并完成配置' },
];

// 步骤切换
const goToStep = (step: number) => {
  if (step < 0 || step >= steps.length) return;

  // 验证当前步骤是否可以离开
  if (step > currentStep.value && !canProceedFromCurrentStep()) {
    return;
  }

  currentStep.value = step;
};

// 检查当前步骤是否可以继续
const canProceedFromCurrentStep = (): boolean => {
  switch (currentStep.value) {
    case 0: {
      // 选择API
      if (selectedApiIds.value.length === 0) {
        ElMessage.warning('请至少选择一个API端点');
        return false;
      }
      return true;
    }
    case 1: {
      // 配置工具
      const hasEmptyToolConfig = toolConfigs.value.some(
        (tool) =>
          !tool.toolName.trim() ||
          !tool.displayName.trim() ||
          !tool.toolDescription.trim(),
      );
      if (hasEmptyToolConfig) {
        ElMessage.warning('请完善所有工具的配置信息');
        return false;
      }
      return true;
    }
    case 2: {
      // 参数转换
      // 参数转换是可选的，可以直接跳过
      return true;
    }
    default: {
      return true;
    }
  }
};

// 下一步
const nextStep = () => {
  if (canProceedFromCurrentStep()) {
    goToStep(currentStep.value + 1);
  }
};

// 上一步
const previousStep = () => {
  goToStep(currentStep.value - 1);
};

// 获取方法标签类型
const getMethodTagType = (method: string) => {
  const typeMap: Record<string, string> = {
    GET: 'primary',
    POST: 'success',
    PUT: 'warning',
    DELETE: 'danger',
  };
  return typeMap[method] || '';
};
</script>

<template>
  <div class="add-api-tool-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <h1 class="page-title">添加API工具</h1>
      <p class="page-description">将REST API端点配置为AI可调用的工具</p>
    </div>

    <!-- 步骤导航 -->
    <el-card class="steps-card">
      <el-steps :active="currentStep" finish-status="success" align-center>
        <el-step
          v-for="(step, index) in steps"
          :key="index"
          :title="step.title"
          :description="step.description"
          @click="goToStep(index)"
        />
      </el-steps>
    </el-card>

    <!-- 步骤1：选择API -->
    <div v-if="currentStep === 0" class="step-content">
      <el-card>
        <template #header>
          <div class="card-header">
            <h3>选择要添加的API端点</h3>
          </div>
        </template>

        <!-- 搜索框 -->
        <div class="search-section">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索API名称、路径或方法类型..."
            clearable
            prefix-icon="Search"
            size="large"
          />
        </div>

        <!-- API列表 -->
        <div v-if="loading" class="loading-section">
          <el-skeleton :rows="6" animated />
        </div>

        <div v-else-if="error" class="error-section">
          <el-empty description="数据加载失败，请刷新页面重试">
            <el-button type="primary" @click="fetchApiEndpoints">
              重新加载
            </el-button>
          </el-empty>
        </div>

        <div v-else-if="filteredApiGroups.length === 0" class="empty-section">
          <el-empty description="暂无可配置的API端点" />
        </div>

        <div v-else class="api-list">
          <div
            v-for="group in filteredApiGroups"
            :key="group.groupName"
            class="api-group"
          >
            <!-- 分组头部 -->
            <div class="api-group-header">
              <el-checkbox
                :indeterminate="isGroupIndeterminate(group)"
                :model-value="isGroupAllSelected(group)"
                @change="
                  (checked: boolean) => handleGroupSelection(group, checked)
                "
              >
                <span class="group-name">{{ group.groupName }}</span>
                <span class="group-count"
                  >({{ group.endpoints.length }}个端点)</span
                >
              </el-checkbox>
            </div>

            <!-- API端点列表 -->
            <div class="api-endpoints">
              <div
                v-for="endpoint in group.endpoints"
                :key="endpoint.id"
                class="api-endpoint"
                :class="{ selected: selectedApiIds.includes(endpoint.id) }"
                @click="
                  handleApiSelection(
                    endpoint.id,
                    !selectedApiIds.includes(endpoint.id),
                  )
                "
              >
                <el-checkbox
                  :model-value="selectedApiIds.includes(endpoint.id)"
                  @change="
                    (checked: boolean) =>
                      handleApiSelection(endpoint.id, checked)
                  "
                  @click.stop
                />
                <el-tag
                  :type="getMethodTagType(endpoint.method)"
                  class="method-tag"
                >
                  {{ endpoint.method }}
                </el-tag>
                <div class="endpoint-info">
                  <div class="endpoint-path">{{ endpoint.path }}</div>
                  <div class="endpoint-description">
                    {{ endpoint.description }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 提示信息 -->
        <el-alert type="info" :closable="false" class="info-alert">
          <template #title>
            <div class="alert-content">
              <el-icon><i class="el-icon-info"></i></el-icon>
              <span
                >选择的每个API端点都将成为MCP服务器中的一个工具（Tool）。建议选择功能相关的API组成完整的服务能力。</span
              >
            </div>
          </template>
        </el-alert>

        <!-- 操作按钮 -->
        <div class="step-actions">
          <el-button
            type="primary"
            :disabled="!canProceedToNext"
            @click="nextStep"
          >
            下一步：配置工具
          </el-button>
        </div>
      </el-card>
    </div>

    <!-- 步骤2：配置工具 -->
    <div v-if="currentStep === 1" class="step-content">
      <el-card>
        <template #header>
          <div class="card-header">
            <h3>配置工具定义</h3>
            <p>为每个选中的API端点配置AI友好的工具名称和描述</p>
          </div>
        </template>

        <div class="tool-configs">
          <div
            v-for="(tool, index) in toolConfigs"
            :key="tool.apiId"
            class="tool-config-card"
          >
            <div class="tool-header">
              <div class="tool-info">
                <h4 class="tool-title">
                  工具{{ index + 1 }}：{{ tool.description }}
                </h4>
                <div class="tool-endpoint">
                  <el-tag
                    :type="getMethodTagType(tool.method)"
                    effect="plain"
                    size="small"
                  >
                    {{ tool.method }}
                  </el-tag>
                  <code class="endpoint-path">{{ tool.path }}</code>
                </div>
              </div>
            </div>

            <el-form :model="tool" label-width="140px" class="tool-form">
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="工具名称" required>
                    <el-input
                      v-model="tool.toolName"
                      placeholder="例如：listOrders"
                    />
                    <div class="form-help">使用动词+名词的驼峰命名</div>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="工具显示名称" required>
                    <el-input
                      v-model="tool.displayName"
                      placeholder="例如：查询订单列表"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-form-item label="工具描述" required>
                <el-input
                  v-model="tool.toolDescription"
                  type="textarea"
                  :rows="3"
                  placeholder="告诉AI何时使用这个工具，包括功能、适用场景、关键参数等"
                />
              </el-form-item>
            </el-form>
          </div>
        </div>

        <div class="step-actions">
          <el-button @click="previousStep">上一步</el-button>
          <el-button type="primary" @click="nextStep">
            下一步：配置参数转换
          </el-button>
        </div>
      </el-card>
    </div>

    <!-- 步骤3：参数转换配置 -->
    <div v-if="currentStep === 2" class="step-content">
      <el-card class="border-0 shadow-sm">
        <template #header>
          <div class="pb-4">
            <h3 class="mb-2 text-lg font-semibold text-gray-900">
              参数转换配置
            </h3>
            <p class="text-sm text-gray-600">
              配置AI理解的自然语言参数如何映射到API所需的技术参数
            </p>
          </div>
        </template>

        <div class="space-y-8">
          <div
            v-for="tool in toolConfigs"
            :key="tool.apiId"
            class="rounded-lg border border-gray-200 bg-gray-50 p-6"
          >
            <!-- 工具头部信息 -->
            <div class="mb-6">
              <h4 class="mb-2 text-base font-medium text-gray-900">
                工具：{{ tool.toolName || tool.displayName }} ({{
                  tool.description
                }})
              </h4>
              <div class="flex items-center gap-2 text-sm">
                <el-tag
                  :type="getMethodTagType(tool.method)"
                  effect="plain"
                  size="small"
                  class="font-mono"
                >
                  {{ tool.method }}
                </el-tag>
                <code
                  class="rounded bg-gray-100 px-2 py-1 font-mono text-xs text-gray-700"
                >
                  {{ tool.path }}
                </code>
              </div>
            </div>

            <!-- 参数转换器列表 -->
            <div class="space-y-6">
              <div
                v-for="(transformer, index) in paramTransformers[tool.apiId] ||
                []"
                :key="index"
                class="param-transformer rounded-lg border border-gray-200 bg-white p-6"
              >
                <!-- 参数转换器头部 -->
                <div class="mb-4 flex items-center justify-between">
                  <div class="flex items-center gap-3">
                    <span class="text-sm font-medium text-gray-700">
                      {{ transformer.aiParam || `参数${index + 1}` }}
                    </span>
                    <el-tag
                      v-if="transformer.transformRule === 'builtin_time'"
                      type="primary"
                      size="small"
                      effect="light"
                    >
                      时间解析器
                    </el-tag>
                    <el-tag
                      v-else-if="transformer.transformRule === 'mapping'"
                      type="success"
                      size="small"
                      effect="light"
                    >
                      映射表
                    </el-tag>
                    <el-tag
                      v-else-if="transformer.transformRule === 'custom'"
                      type="warning"
                      size="small"
                      effect="light"
                    >
                      自定义函数
                    </el-tag>
                  </div>
                  <el-button
                    v-if="(paramTransformers[tool.apiId] || []).length > 1"
                    type="danger"
                    size="small"
                    circle
                    icon="Delete"
                    @click="removeParameterTransformer(tool.apiId, index)"
                  />
                </div>

                <!-- 时间范围参数示例 -->
                <div class="mb-6 grid grid-cols-1 gap-6 lg:grid-cols-2">
                  <!-- AI参数预览 -->
                  <div>
                    <label class="mb-2 block text-sm font-medium text-gray-700">
                      AI参数
                    </label>
                    <div
                      class="preview-box rounded-lg border border-blue-200 bg-blue-50 p-4"
                    >
                      <div class="mb-2 text-xs text-blue-600">
                        AI可能提供的参数形式：
                      </div>
                      <div class="font-mono text-sm text-blue-800">
                        <div v-if="transformer.aiParam === 'timeRange'">
                          timeRange: "最近7天"
                        </div>
                        <div v-else-if="transformer.aiParam === 'orderStatus'">
                          orderStatus: "待发货"
                        </div>
                        <div v-else>
                          {{ transformer.aiParam || 'paramName' }}: "value"
                        </div>
                      </div>
                      <el-input
                        v-model="transformer.aiParam"
                        placeholder="如：timeRange, orderStatus"
                        size="small"
                        class="mt-3"
                      />
                    </div>
                  </div>

                  <!-- API参数预览 -->
                  <div>
                    <label class="mb-2 block text-sm font-medium text-gray-700">
                      API参数
                    </label>
                    <div
                      class="preview-box rounded-lg border border-green-200 bg-green-50 p-4"
                    >
                      <div class="mb-2 text-xs text-green-600">
                        转换后传给API的参数形式：
                      </div>
                      <div class="font-mono text-sm text-green-800">
                        <div
                          v-if="transformer.transformRule === 'builtin_time'"
                        >
                          startDate: "2024-01-09"<br />
                          endDate: "2024-01-15"
                        </div>
                        <div
                          v-else-if="transformer.transformRule === 'mapping'"
                        >
                          status: "pending_shipment"
                        </div>
                        <div v-else>
                          {{ transformer.apiParam || 'apiParam' }}:
                          "converted_value"
                        </div>
                      </div>
                      <el-input
                        v-model="transformer.apiParam"
                        placeholder="如：startDate, endDate, status"
                        size="small"
                        class="mt-3"
                      />
                    </div>
                  </div>
                </div>

                <!-- 转换规则选择 -->
                <div class="mb-4">
                  <label class="mb-2 block text-sm font-medium text-gray-700">
                    转换规则
                  </label>
                  <el-select
                    v-model="transformer.transformRule"
                    placeholder="请选择转换规则"
                    class="w-full"
                  >
                    <el-option
                      v-for="option in transformRuleOptions"
                      :key="option.value"
                      :label="option.label"
                      :value="option.value"
                    />
                  </el-select>
                </div>

                <!-- 支持的表达式示例 -->
                <div
                  v-if="transformer.transformRule === 'builtin_time'"
                  class="mb-4"
                >
                  <div class="rounded-lg border border-blue-200 bg-blue-50 p-4">
                    <div class="mb-2 text-sm font-medium text-blue-800">
                      支持的表达式示例：
                    </div>
                    <div class="space-y-1 text-xs text-blue-600">
                      <div>• "今天" → 今天的开始和结束时间</div>
                      <div>• "最近7天" → 7天前到今天</div>
                      <div>• "上个月" → 上个月的开始和结束时间</div>
                      <div>• "2024年1月" → 指定年月的时间范围</div>
                    </div>
                  </div>
                </div>

                <!-- 映射表配置 -->
                <div
                  v-if="transformer.transformRule === 'mapping'"
                  class="mb-4"
                >
                  <label class="mb-2 block text-sm font-medium text-gray-700">
                    映射配置
                  </label>
                  <el-input
                    v-model="transformer.mappingTable"
                    type="textarea"
                    :rows="6"
                    placeholder='{&#10;  "待付款": "pending_payment",&#10;  "待发货": "pending_shipment",&#10;  "已发货": "shipped",&#10;  "已完成": "completed",&#10;  "已取消": "cancelled"&#10;}'
                    class="font-mono text-sm"
                  />
                </div>

                <!-- 自定义函数配置 -->
                <div v-if="transformer.transformRule === 'custom'" class="mb-4">
                  <label class="mb-2 block text-sm font-medium text-gray-700">
                    自定义转换函数
                  </label>
                  <el-input
                    v-model="transformer.customFunction"
                    type="textarea"
                    :rows="10"
                    :placeholder="customFunctionTemplate"
                    class="font-mono text-sm"
                  />
                </div>
              </div>
            </div>

            <!-- 添加参数按钮 -->
            <div class="mt-6">
              <el-button
                type="primary"
                size="small"
                @click="addParameterTransformer(tool.apiId)"
                class="bg-blue-600 hover:bg-blue-700"
              >
                + 添加参数转换
              </el-button>
            </div>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div
          class="mt-8 flex items-center justify-between border-t border-gray-200 pt-6"
        >
          <el-button @click="previousStep" class="px-6"> 上一步 </el-button>
          <el-button type="primary" @click="nextStep" class="px-6">
            下一步：测试与完成
          </el-button>
        </div>
      </el-card>
    </div>

    <!-- 步骤4：测试与完成 -->
    <div v-if="currentStep === 3" class="step-content">
      <el-card>
        <template #header>
          <div class="card-header">
            <h3>测试工具</h3>
            <p>模拟AI调用测试完整调用流程</p>
          </div>
        </template>

        <div class="test-section">
          <el-form-item label="模拟AI调用测试">
            <el-input
              v-model="testQuery"
              placeholder="例如：帮我查看最近7天的待发货订单"
              size="large"
            />
          </el-form-item>

          <el-button
            type="primary"
            size="large"
            :loading="testLoading"
            @click="runTest"
          >
            <template v-if="testLoading">
              <el-icon class="is-loading">
                <i class="el-icon-loading"></i>
              </el-icon>
              运行测试中...
            </template>
            <template v-else> 运行测试 </template>
          </el-button>
        </div>

        <!-- 测试结果 -->
        <div v-if="testResults.visible" class="test-results">
          <el-alert
            :type="testResults.success ? 'success' : 'error'"
            :title="
              testResults.success
                ? '测试成功！工具运行正常。'
                : '测试失败，请检查配置'
            "
            show-icon
            class="mb-4"
          />

          <div v-if="testResults.success" class="result-steps">
            <div class="result-step">
              <h4 class="result-title">1. AI理解结果</h4>
              <div class="result-content">
                <pre>{{ testResults.aiUnderstanding }}</pre>
              </div>
            </div>

            <div class="result-step">
              <h4 class="result-title">2. 参数转换结果</h4>
              <div class="result-content">
                <pre>{{ testResults.parameterTransform }}</pre>
              </div>
            </div>

            <div class="result-step">
              <h4 class="result-title">3. API响应</h4>
              <div class="result-content">
                <pre>{{ testResults.apiResponse }}</pre>
              </div>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 完成配置 -->
      <el-card class="finish-card">
        <template #header>
          <div class="card-header">
            <h3>完成配置</h3>
          </div>
        </template>

        <el-form :model="finishForm" label-width="120px">
          <el-form-item label="工具分组">
            <el-select
              v-model="finishForm.toolGroup"
              placeholder="选择分组（可选）"
              style="width: 100%"
            >
              <el-option label="电商助手" value="ecommerce" />
              <el-option label="客服工具" value="customer_service" />
              <el-option label="+ 创建新分组" value="new_group" />
            </el-select>
            <div class="form-help">将相关工具组织在一起，方便管理和授权</div>
          </el-form-item>

          <el-form-item label="添加说明">
            <el-input
              v-model="finishForm.description"
              type="textarea"
              :rows="3"
              placeholder="描述这些工具的功能和使用场景..."
            />
          </el-form-item>
        </el-form>

        <div class="step-actions">
          <el-button @click="previousStep">上一步</el-button>
          <el-button type="success" size="large" @click="saveToolConfiguration">
            保存工具配置
          </el-button>
        </div>
      </el-card>
    </div>
  </div>
</template>

<style scoped>
/* 页面布局 */
.add-api-tool-page {
  padding: 24px;
  background: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #1a202c;
  margin: 0 0 8px 0;
}

.page-description {
  color: #64748b;
  margin: 0;
}

/* 步骤导航 */
.steps-card {
  margin-bottom: 24px;
}

/* 步骤内容 */
.step-content {
  margin-bottom: 24px;
}

.card-header h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: #1a202c;
}

.card-header p {
  margin: 0;
  color: #64748b;
  font-size: 14px;
}

/* 搜索区域 */
.search-section {
  margin-bottom: 24px;
}

/* API列表 */
.api-list {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
}

.api-group {
  border-bottom: 1px solid #e5e7eb;
}

.api-group:last-child {
  border-bottom: none;
}

.api-group-header {
  padding: 16px;
  background: #f9fafb;
  border-bottom: 1px solid #f3f4f6;
}

.group-name {
  font-weight: 600;
  color: #1a202c;
}

.group-count {
  font-weight: normal;
  color: #6b7280;
  margin-left: 8px;
}

.api-endpoints {
  background: white;
}

.api-endpoint {
  display: flex;
  align-items: center;
  padding: 12px 16px 12px 48px;
  border-bottom: 1px solid #f3f4f6;
  cursor: pointer;
  transition: background-color 0.2s;
}

.api-endpoint:last-child {
  border-bottom: none;
}

.api-endpoint:hover {
  background: #f9fafb;
}

.api-endpoint.selected {
  background: #eff6ff;
}

.method-tag {
  margin: 0 12px;
  min-width: 60px;
  text-align: center;
}

.endpoint-info {
  flex: 1;
}

.endpoint-path {
  font-family: 'Monaco', 'Consolas', monospace;
  font-size: 14px;
  font-weight: 500;
  color: #1a202c;
  margin-bottom: 4px;
}

.endpoint-description {
  font-size: 12px;
  color: #6b7280;
}

/* 提示信息 */
.info-alert {
  margin: 24px 0;
}

.alert-content {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 工具配置 */
.tool-configs {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.tool-config-card {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 20px;
  background: #f9fafb;
}

.tool-header {
  margin-bottom: 20px;
}

.tool-title {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1a202c;
}

.tool-endpoint {
  display: flex;
  align-items: center;
  gap: 8px;
}

.endpoint-path {
  font-family: 'Monaco', 'Consolas', monospace;
  font-size: 12px;
  color: #6b7280;
  background: #f3f4f6;
  padding: 2px 6px;
  border-radius: 4px;
}

.form-help {
  font-size: 12px;
  color: #6b7280;
  margin-top: 4px;
}

/* 参数转换配置 */
.param-transform-configs {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.tool-param-config {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 24px;
  background: #f9fafb;
}

.tool-section-header {
  margin-bottom: 20px;
}

.tool-section-header h4 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1a202c;
}

.tool-description {
  margin: 0;
  font-size: 14px;
  color: #6b7280;
}

.param-transformer-card {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  padding: 16px;
  margin-bottom: 16px;
}

.transformer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.transformer-title {
  font-weight: 600;
  color: #1a202c;
}

.transformer-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.preview-section {
  margin-bottom: 16px;
}

.preview-title {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 8px;
}

.preview-box {
  background: #f8fafc;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  padding: 12px;
}

/* 测试区域 */
.test-section {
  margin-bottom: 24px;
}

.test-results {
  margin-top: 24px;
}

.result-steps {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.result-step {
  background: #f8fafc;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  padding: 16px;
}

.result-title {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #374151;
}

.result-content pre {
  background: #1e293b;
  color: #e2e8f0;
  padding: 12px;
  border-radius: 6px;
  font-size: 13px;
  line-height: 1.5;
  margin: 0;
  overflow-x: auto;
  font-family: 'Monaco', 'Consolas', monospace;
}

/* 完成配置 */
.finish-card {
  margin-top: 24px;
}

/* 操作按钮 */
.step-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 24px;
  padding-top: 24px;
  border-top: 1px solid #e5e7eb;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .add-api-tool-page {
    padding: 16px;
  }

  .api-endpoint {
    padding-left: 32px;
  }

  .step-actions {
    flex-direction: column;
    gap: 12px;
  }

  .step-actions .el-button {
    width: 100%;
  }
}
</style>
