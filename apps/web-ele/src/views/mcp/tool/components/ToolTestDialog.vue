<script lang="ts" setup>
import type { ExtendedMcpTool, TestResults } from '../types';

import { computed, ref, watch } from 'vue';

import { ElButton, ElDialog, ElInput, ElMessage } from 'element-plus';

interface Props {
  visible: boolean;
  tool: ExtendedMcpTool | null;
}

interface Emits {
  (e: 'update:visible', value: boolean): void;
  (e: 'close'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 测试相关状态
const testQuery = ref('');
const testLoading = ref(false);
const testResults = ref<TestResults>({
  visible: false,
  success: false,
  aiUnderstanding: '',
  parameterTransform: '',
  apiResponse: '',
});

// 计算属性：弹窗显示状态
const dialogVisible = computed({
  get: () => props.visible,
  set: (value: boolean) => {
    emit('update:visible', value);
    if (!value) {
      emit('close');
    }
  },
});

// 运行测试
const runTest = async () => {
  if (!testQuery.value.trim()) {
    ElMessage.warning('请输入测试查询');
    return;
  }

  testLoading.value = true;
  try {
    // 模拟调用API测试
    await new Promise((resolve) => setTimeout(resolve, 2000));

    testResults.value = {
      visible: true,
      success: true,
      aiUnderstanding: `选择工具：${props.tool?.toolName}\n参数解析：{\n  "timeRange": "最近7天",\n  "orderStatus": "待发货"\n}`,
      parameterTransform: `API参数：{\n  "startDate": "2024-01-09",\n  "endDate": "2024-01-15",\n  "status": "pending_shipment"\n}`,
      apiResponse: `{\n  "success": true,\n  "data": {\n    "total": 15,\n    "orders": [\n      {"id": "ORD-2024-0156", "customer": "张三", "amount": 299.00},\n      {"id": "ORD-2024-0157", "customer": "李四", "amount": 1580.00}\n    ]\n  }\n}`,
    };

    ElMessage.success('测试成功！');
  } catch {
    testResults.value.success = false;
    ElMessage.error('测试失败，请检查配置');
  } finally {
    testLoading.value = false;
  }
};

// 重置测试状态
const resetTest = () => {
  testQuery.value = '';
  testResults.value.visible = false;
  testLoading.value = false;
};

// 监听弹窗打开，重置状态
watch(
  () => props.visible,
  (newVisible) => {
    if (newVisible) {
      resetTest();
    }
  },
);
</script>

<template>
  <ElDialog
    v-model="dialogVisible"
    title="测试工具"
    width="800px"
    :close-on-click-modal="false"
    @closed="resetTest"
  >
    <!-- <div v-if="tool" class="space-y-4">
      <div class="mb-4">
        <h4 class="mb-2 text-lg font-medium text-gray-900">
          {{ tool.toolName }}
        </h4>
        <p class="text-sm text-gray-600">模拟AI调用测试，请输入自然语言查询</p>
      </div>

      <div>
        <label class="mb-2 block text-sm font-medium text-gray-700">
          测试查询
        </label>
        <ElInput
          v-model="testQuery"
          type="textarea"
          :rows="3"
          placeholder="例如：查看最近7天的待发货订单"
        />
      </div>

      <div class="flex justify-end">
        <ElButton type="primary" :loading="testLoading" @click="runTest">
          运行测试
        </ElButton>
      </div>

      <div v-if="testResults.visible" class="mt-6">
        <div
          v-if="testResults.success"
          class="mb-4 rounded-lg border border-green-200 bg-green-50 p-4"
        >
          <div class="flex items-center">
            <div class="mr-2 h-2 w-2 rounded-full bg-green-500"></div>
            <span class="text-sm font-medium text-green-800">
              测试成功！工具运行正常。
            </span>
          </div>
        </div>

        <div class="space-y-4">
          <div class="rounded-lg border p-4">
            <h6
              class="mb-3 flex items-center text-sm font-medium text-gray-700"
            >
              <span class="mr-2 h-2 w-2 rounded-full bg-blue-500"></span>
              1. AI理解结果
            </h6>
            <pre
              class="whitespace-pre-wrap rounded bg-gray-50 p-3 text-sm text-gray-800"
              >{{ testResults.aiUnderstanding }}</pre
            >
          </div>

          <div class="rounded-lg border p-4">
            <h6
              class="mb-3 flex items-center text-sm font-medium text-gray-700"
            >
              <span class="mr-2 h-2 w-2 rounded-full bg-yellow-500"></span>
              2. 参数转换结果
            </h6>
            <pre
              class="whitespace-pre-wrap rounded bg-gray-50 p-3 text-sm text-gray-800"
              >{{ testResults.parameterTransform }}</pre
            >
          </div>

          <div class="rounded-lg border p-4">
            <h6
              class="mb-3 flex items-center text-sm font-medium text-gray-700"
            >
              <span class="mr-2 h-2 w-2 rounded-full bg-green-500"></span>
              3. API响应
            </h6>
            <pre
              class="whitespace-pre-wrap rounded bg-gray-50 p-3 text-sm text-gray-800"
              >{{ testResults.apiResponse }}</pre
            >
          </div>
        </div>
      </div>
    </div> -->
    <el-card class="test-section" shadow="hover">
      <template #header>
        <div class="card-header">
          <h3>自然语言测试</h3>
          <p>输入一条测试语句，系统将模拟 AI 调用并返回理解和 API 请求。</p>
        </div>
      </template>

      <ElInput
        v-model="testQuery"
        placeholder="例如：查看最近7天的待发货订单"
        type="textarea"
        :rows="3"
      />

      <ElButton
        type="primary"
        :loading="testLoading"
        class="mt-4"
        @click="runTest"
      >
        运行测试
      </ElButton>

      <div class="test-results" v-if="testResults.visible">
        <el-alert
          v-if="testResults.success"
          title="测试成功"
          type="success"
          show-icon
          class="mb-4"
        />
        <el-alert
          v-else
          title="测试失败，请检查配置"
          type="error"
          show-icon
          class="mb-4"
        />

        <div class="result-steps">
          <div class="result-step">
            <p class="result-title">AI 理解</p>
            <pre>{{ testResults.aiUnderstanding }}</pre>
          </div>
          <div class="result-step">
            <p class="result-title">参数转换</p>
            <pre>{{ testResults.parameterTransform }}</pre>
          </div>
          <div class="result-step">
            <p class="result-title">API 响应</p>
            <pre>{{ testResults.apiResponse }}</pre>
          </div>
        </div>
      </div>
    </el-card>
  </ElDialog>
</template>

<style scoped>
/* 使用Tailwind CSS，无需额外样式 */
.test-results {
  margin-top: 24px;
}

.result-steps {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.result-step {
  background: #f8fafc;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  padding: 16px;
}

.result-title {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #374151;
}

.result-step pre {
  background: #1e293b;
  color: #e2e8f0;
  padding: 12px;
  border-radius: 6px;
  font-size: 13px;
  line-height: 1.5;
  margin: 0;
  overflow-x: auto;
  font-family: 'Monaco', 'Consolas', monospace;
}
</style>
