<!--  eslint-disable @typescript-eslint/no-unused-vars   -->
<!-- eslint-disable unused-imports/no-unused-imports  -->
<script lang="ts" setup>
import type { VbenFormProps } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';
import type { McpTool } from '#/api/mcp';

import { computed, ref } from 'vue';
import { useRouter } from 'vue-router';

import { Page } from '@vben/common-ui';

import { ElButton, ElCard, ElMessage, ElMessageBox, ElTag } from 'element-plus';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  offLineMcpToolApi,
  onLineMcpToolApi,
  pageQueryMcpAppApi,
  pageQueryMcpToolApi,
} from '#/api/mcp';

import ToolTestDialog from './components/ToolTestDialog.vue';
import { getMethodTagType, getStatusTagType } from './tool-add/utils/apiUtils';

interface ExtendedMcpTool extends McpTool {
  callCount: number;
  successRate: number;
}

const router = useRouter();

// 分组选项数据
const groupOptions = ref<Array<{ label: string; value: string }>>([]);
const groupLoading = ref(false);

// 获取分组选项的API函数
const fetchGroupOptions = async (searchKey = '') => {
  try {
    groupLoading.value = true;
    const result = await pageQueryMcpAppApi({
      currentPage: 1,
      pageSize: 100, // 一次性获取较多数据
      searchKey,
    });
    if (result && result.data) {
      // 将API返回的数据转换为选项格式
      const options = result.data.map((item: any) => ({
        label: item.appName || item.name,
        value: item.appName || item.name || item.value,
      }));
      // 在开头添加"全部分组"选项（仅在没有搜索关键词时添加）
      groupOptions.value = searchKey
        ? options
        : [{ label: '全部分组', value: '' }, ...options];
    } else {
      groupOptions.value = [];
    }
  } catch (error) {
    console.error('获取分组选项失败:', error);
    // 接口出错时返回假数据
    const fallbackOptions = [
      { label: '电商助手', value: '电商助手' },
      { label: '客服工具', value: '客服工具' },
      { label: '库存管理', value: '库存管理' },
      { label: '订单管理', value: '订单管理' },
    ];

    // 如果有搜索关键词，过滤假数据
    if (searchKey) {
      const filtered = fallbackOptions.filter((option) =>
        option.label.toLowerCase().includes(searchKey.toLowerCase()),
      );
      groupOptions.value = filtered;
    } else {
      // 没有搜索关键词时返回完整的假数据（包含"全部分组"）
      groupOptions.value = [
        { label: '全部分组', value: '' },
        ...fallbackOptions,
      ];
    }
  } finally {
    groupLoading.value = false;
  }
};

// 远程搜索方法
const remoteMethod = (query: string) => {
  fetchGroupOptions(query);
};

// 初始化加载分组选项
fetchGroupOptions();

// Mock数据
const mockTools = ref<ExtendedMcpTool[]>([
  {
    toolKey: '1',
    toolName: 'listOrders',
    description: '查询订单列表',
    apiMethod: 'GET',
    apiUrl: '/api/v2/orders',
    toolAppNameList: ['电商助手'],
    callCount: 12_456,
    successRate: 99.8,
    toolStatus: 'enabled',
    createdAt: '2024-01-15',
    updatedAt: '2024-01-16',
  },
  {
    toolKey: '2',
    toolName: 'getOrderDetail',
    description: '查询订单详情',
    apiMethod: 'GET',
    apiUrl: '/api/v2/orders/{id}',
    toolAppNameList: ['电商助手'],
    callCount: 8234,
    successRate: 99.5,
    toolStatus: 'enabled',
    createdAt: '2024-01-15',
    updatedAt: '2024-01-16',
  },
  {
    toolKey: '3',
    toolName: 'searchProducts',
    description: '搜索商品',
    apiMethod: 'GET',
    apiUrl: '/api/v2/products/search',
    toolAppNameList: ['电商助手'],
    callCount: 15_678,
    successRate: 98.2,
    toolStatus: 'enabled',
    createdAt: '2024-01-14',
    updatedAt: '2024-01-15',
  },
  {
    toolKey: '4',
    toolName: 'createOrder',
    description: '创建订单',
    apiMethod: 'POST',
    apiUrl: '/api/v2/orders',
    toolAppNameList: ['电商助手'],
    callCount: 3456,
    successRate: 96.5,
    toolStatus: 'maintenance',
    createdAt: '2024-01-13',
    updatedAt: '2024-01-14',
  },
]);

// 统计信息
const statistics = computed(() => {
  const total = mockTools.value.length;
  const enabled = mockTools.value.filter(
    (t) => t.toolStatus === 'enabled',
  ).length;
  const disabled = mockTools.value.filter(
    (t) => t.toolStatus === 'disabled',
  ).length;
  const maintenance = mockTools.value.filter(
    (t) => t.toolStatus === 'maintenance',
  ).length;
  const totalCalls = mockTools.value.reduce(
    (sum, t) => sum + (t.callCount || 0),
    0,
  );
  const avgSuccessRate =
    total > 0
      ? (
          mockTools.value.reduce((sum, t) => sum + (t.successRate || 0), 0) /
          total
        ).toFixed(1)
      : '0.0';

  return {
    total,
    enabled,
    disabled,
    maintenance,
    totalCalls,
    avgSuccessRate,
  };
});

// 表单配置
const formOptions: VbenFormProps = {
  collapsed: false,
  showCollapseButton: false,
  schema: [
    {
      component: 'Input',
      componentProps: {
        clearable: true,
        placeholder: '请输入工具名称',
      },
      fieldName: 'toolName',
      label: '工具名称',
    },
    {
      component: 'Select',
      componentProps: {
        clearable: true,
        filterable: true,
        placeholder: '请选择分组',
        remote: true,
        remoteMethod,
        loading: groupLoading,
        options: groupOptions,
        reserveKeyword: true,
        // api: fetchGroupOptions,
        // params: groupSearchParams,
        // immediate: false,
        // alwaysLoad: false,
      },
      fieldName: 'group',
      label: '所属分组',
    },
    {
      component: 'Select',
      componentProps: {
        clearable: true,
        options: [
          { label: '启用', value: 'enabled' },
          { label: '禁用', value: 'disabled' },
          { label: '维护中', value: 'maintenance' },
        ],
        placeholder: '请选择状态',
      },
      fieldName: 'toolStatus',
      label: '状态',
    },
  ],
  submitButtonOptions: {
    content: '查询',
  },
  // 是否在字段值改变时提交表单
  submitOnChange: false,
  // 按下回车时是否提交表单
  submitOnEnter: false,
};

// 获取状态文本
const getStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    enabled: '启用',
    disabled: '禁用',
    maintenance: '维护中',
  };
  return textMap[status] || status;
};

// 格式化数字
const formatNumber = (num: number) => {
  return num.toLocaleString();
};

// 表格配置
const gridOptions: VxeGridProps<ExtendedMcpTool> = {
  minHeight: 600,
  columns: [
    {
      field: 'toolName',
      title: '工具名称',
      minWidth: 150,
      slots: {
        default: 'toolName',
      },
    },
    {
      field: 'apiMethod',
      title: 'API端点',
      minWidth: 250,
      slots: {
        default: 'apiEndpoint',
      },
    },
    {
      field: 'toolAppNameList',
      title: '所属分组',
      width: 120,
      slots: {
        default: 'toolAppNameList',
      },
    },
    {
      field: 'callCount',
      title: '调用次数',
      width: 100,
      align: 'right',
      slots: {
        default: 'callCount',
      },
    },
    {
      field: 'successRate',
      title: '成功率',
      width: 100,
      align: 'right',
      slots: {
        default: 'successRate',
      },
    },
    {
      field: 'toolStatus',
      title: '状态',
      width: 100,
      slots: {
        default: 'toolStatus',
      },
    },
    {
      field: 'createdAt',
      title: '创建时间',
      width: 120,
    },
    {
      title: '操作',
      width: 180,
      slots: {
        default: 'action',
      },
    },
  ],
  keepSource: true,
  pagerConfig: {},
  // data: mockTools.value,
  showOverflow: false, // 关闭溢出隐藏，允许内容换行
  border: true,
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        try {
          // 使用真实API调用
          const { currentPage, pageSize } = page;
          const params = {
            currentPage,
            pageSize,
            ...formValues,
            mcpAppId: formValues?.mcpAppId, // 应用appId
            toolKey: formValues?.toolName, // 工具key
            toolStatus: formValues?.toolStatus, // 工具状态
          };

          // 调用API获取数据
          const result = await pageQueryMcpToolApi(params);
          // 将API返回的数据转换为表格需要的格式
          if (result) {
            const { data, totalCount } = result;
            // 将API返回的数据映射到表格需要的字段
            const items = data.map((item: McpTool) => ({
              ...item,
            }));
            return {
              items,
              total: totalCount,
            };
          }
          throw new Error('API返回数据格式不正确');
        } catch (error) {
          console.error('获取tool列表失败:', error);
          return {
            items: [],
            total: 0,
          };
        }
      },
    },
  },
};

const [Grid, GridApi] = useVbenVxeGrid({ formOptions, gridOptions });

// 跳转到添加工具页面
const goToAddTool = () => {
  router.push('/mcp/tool/add');
};

// 编辑工具
const editTool = (row: ExtendedMcpTool) => {
  router.push(`/mcp/tool/edit/${row.id}`);
};

// 测试工具
const previewTool = (tool: ExtendedMcpTool) => {
  currentTestTool.value = tool;
  testDialogVisible.value = true;
};

// 上线工具
const onLineTool = async (tool: ExtendedMcpTool) => {
  try {
    await ElMessageBox.confirm(
      `确定要发布工具 "${tool.toolName}" 吗？`,
      '确认发布',
      {
        confirmButtonText: '发布',
        cancelButtonText: '取消',
        type: 'warning',
        confirmButtonClass: 'el-button--primary',
      },
    );

    // 调用发布接口
    if (tool.toolKey) {
      await onLineMcpToolApi({ toolId: tool.toolKey });
      ElMessage.success('工具发布成功');
      GridApi.reload();
    } else {
      ElMessage.error('工具ID不存在');
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('工具发布失败');
      console.error('Publish tool error:', error);
    }
  }
};

// 下线工具
const offLineTool = async (tool: ExtendedMcpTool) => {
  try {
    await ElMessageBox.confirm(
      `确定要下线工具 "${tool.toolName}" 吗？`,
      '确认下线',
      {
        confirmButtonText: '下线',
        cancelButtonText: '取消',
        type: 'warning',
        confirmButtonClass: 'el-button--danger',
      },
    );
    // 请求接口处理数据
    if (tool.toolKey) {
      await offLineMcpToolApi({ toolId: tool.toolKey });
      ElMessage.success('工具下线成功');
      GridApi.reload();
    } else {
      ElMessage.error('工具ID不存在');
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('下线工具失败');
      console.error('Offline tool error:', error);
    }
  }
};

// 测试弹窗相关状态
const testDialogVisible = ref(false);
const currentTestTool = ref<ExtendedMcpTool | null>(null);
</script>

<template>
  <Page title="API工具列表" description="管理MCP服务器中的所有API工具">
    <Grid>
      <template #toolbar>
        <div class="mb-6 flex items-center justify-end gap-2">
          <ElButton type="primary" @click="goToAddTool"> 添加新工具 </ElButton>
        </div>
      </template>

      <template #toolName="{ row }">
        <div class="space-y-1">
          <div class="font-medium text-gray-900">{{ row.toolName }}</div>
          <div class="text-sm text-gray-500">{{ row?.description }}</div>
        </div>
      </template>

      <template #apiEndpoint="{ row }">
        <div class="flex items-center gap-2">
          <ElTag
            :type="getMethodTagType(row.apiMethod)"
            size="small"
            class="font-mono text-xs"
          >
            {{ row.apiMethod }}
          </ElTag>
          <code class="font-mono text-xs text-gray-600">{{ row.apiUrl }}</code>
        </div>
      </template>

      <template #toolAppNameList="{ row }">
        <!-- toolAppNameList TODO   列表数据-->
        <ElTag v-if="row.toolAppNameList" size="small" effect="light">
          {{ row.toolAppNameList }}
        </ElTag>
        <span v-else class="text-gray-400">-</span>
      </template>

      <template #callCount="{ row }">
        <span class="font-mono">{{ formatNumber(row.callCount) }}</span>
      </template>

      <template #successRate="{ row }">
        <span class="font-mono">{{ row.successRate }}%</span>
      </template>

      <template #toolStatus="{ row }">
        <ElTag
          :type="getStatusTagType(row.toolStatus)"
          size="small"
          effect="light"
        >
          {{ getStatusText(row.toolStatus) }}
        </ElTag>
      </template>

      <template #action="{ row }">
        <div class="flex items-center justify-center gap-1">
          <ElButton size="small" type="primary" @click="editTool(row)">
            编辑
          </ElButton>
          <ElButton size="small" @click="previewTool(row)"> 预览 </ElButton>
          <ElButton size="small" @click="onLineTool(row)"> 发布 </ElButton>
          <ElButton size="small" @click="offLineTool(row)"> 下线 </ElButton>
        </div>
      </template>
    </Grid>
    <!-- 统计信息 -->
    <div class="mt-4 grid grid-cols-1 gap-4 md:grid-cols-3 lg:grid-cols-4">
      <ElCard>
        <div class="text-3xl font-bold text-blue-600">
          {{ statistics.total }}
        </div>
        <div class="mt-1 text-sm text-gray-600">总工具数</div>
      </ElCard>

      <ElCard>
        <div class="text-3xl font-bold text-green-600">
          {{ statistics.enabled }}
        </div>
        <div class="mt-1 text-sm text-gray-600">启用中</div>
      </ElCard>

      <ElCard>
        <div class="text-3xl font-bold text-yellow-600">
          {{ statistics.maintenance }}
        </div>
        <div class="mt-1 text-sm text-gray-600">维护中</div>
      </ElCard>

      <ElCard>
        <div class="text-3xl font-bold text-purple-600">
          {{ statistics.avgSuccessRate }}%
        </div>
        <div class="mt-1 text-sm text-gray-600">平均成功率</div>
      </ElCard>
    </div>

    <!-- 测试工具弹窗 -->
    <ToolTestDialog
      v-model:visible="testDialogVisible"
      :tool="currentTestTool"
      @close="currentTestTool = null"
    />
  </Page>
</template>

<style scoped>
/* 使用原子化样式，无需额外CSS */
</style>
