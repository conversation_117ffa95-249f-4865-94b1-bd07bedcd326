<script lang="ts" setup>
import { useToolWizard } from '../composables/useToolWizard';

const {
  testQuery,
  testLoading,
  testResults,
  finishForm,
  runTest,
  saveToolConfiguration,
} = useToolWizard();
</script>

<template>
  <div class="min-h-[600px] bg-gray-50 p-5">
    <!-- 页面标题 -->
    <div class="mb-6">
      <h3 class="m-0 mb-2 text-lg font-semibold text-gray-800">测试与完成</h3>
      <p class="m-0 text-sm text-gray-500">测试工具配置并完成最终设置</p>
    </div>

    <!-- 测试区域 -->
    <div class="mb-6 rounded-lg bg-white p-6 shadow-sm">
      <div class="mb-4">
        <h4 class="m-0 mb-2 text-base font-semibold text-gray-800">
          自然语言测试
        </h4>
        <p class="m-0 text-sm text-gray-500">
          输入一条测试语句，系统将模拟 AI 调用并返回理解和 API 请求。
        </p>
      </div>

      <el-input
        v-model="testQuery"
        placeholder="例如：查看最近7天的待发货订单"
        type="textarea"
        :rows="3"
        class="mb-4"
      />

      <el-button type="primary" :loading="testLoading" @click="runTest">
        运行测试
      </el-button>

      <div v-if="testResults.visible" class="mt-6">
        <el-alert
          v-if="testResults.success"
          title="测试成功"
          type="success"
          show-icon
          class="mb-4"
        />
        <el-alert
          v-else
          title="测试失败，请检查配置"
          type="error"
          show-icon
          class="mb-4"
        />

        <div class="space-y-4">
          <div class="rounded-lg bg-gray-50 p-4">
            <h5 class="mb-2 text-sm font-semibold text-gray-700">AI 理解</h5>
            <pre class="rounded bg-gray-800 p-3 text-sm text-gray-100">{{
              testResults.aiUnderstanding
            }}</pre>
          </div>
          <div class="rounded-lg bg-gray-50 p-4">
            <h5 class="mb-2 text-sm font-semibold text-gray-700">参数转换</h5>
            <pre class="rounded bg-gray-800 p-3 text-sm text-gray-100">{{
              testResults.parameterTransform
            }}</pre>
          </div>
          <div class="rounded-lg bg-gray-50 p-4">
            <h5 class="mb-2 text-sm font-semibold text-gray-700">API 响应</h5>
            <pre class="rounded bg-gray-800 p-3 text-sm text-gray-100">{{
              testResults.apiResponse
            }}</pre>
          </div>
        </div>
      </div>
    </div>

    <!-- 完成配置区域 -->
    <div class="rounded-lg bg-white p-6 shadow-sm">
      <div class="mb-4">
        <h4 class="m-0 mb-2 text-base font-semibold text-gray-800">完成配置</h4>
        <p class="m-0 text-sm text-gray-500">
          填写工具组名称和描述信息，保存当前配置。
        </p>
      </div>

      <el-form :model="finishForm" label-width="120px" label-position="top">
        <el-form-item label="工具组名称">
          <el-input
            v-model="finishForm.toolGroup"
            placeholder="请输入工具组名称"
          />
        </el-form-item>
        <el-form-item label="描述信息">
          <el-input
            type="textarea"
            v-model="finishForm.description"
            :rows="4"
            placeholder="该组工具的说明..."
          />
        </el-form-item>
      </el-form>

      <el-button type="primary" @click="saveToolConfiguration">
        保存工具配置
      </el-button>
    </div>
  </div>
</template>

<style scoped>
/* 使用 Tailwind CSS，移除大部分自定义样式 */
pre {
  font-family: 'Monaco', 'Consolas', 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.5;
  margin: 0;
  overflow-x: auto;
  white-space: pre-wrap;
}
</style>
