<script lang="ts" setup>
import { Delete, InfoFilled, Plus } from '@element-plus/icons-vue';
import { ElTag } from 'element-plus';

import { useApiSelection } from '../composables/useApiSelection';
import { useDialogManager } from '../composables/useDialogManager';
import { useToolWizard } from '../composables/useToolWizard';
import { getMethodTagType } from '../utils/apiUtils';

const {
  Grid,
  selectedApis,
  tempSelectedApis,
  setTableSelection,
  handleDialogCheckboxChange,
  handleProxyQuery,
  confirmApiSelection,
} = useToolWizard();

// API 选择管理
const { selectedCount, removeSelectedApi } = useApiSelection(
  selectedApis,
  tempSelectedApis,
  confirmApiSelection,
);

// 对话框管理
const { dialogVisible, openDialog, confirmDialog, cancelDialog } =
  useDialogManager(
    // onOpen: 对话框打开时的回调
    () => {
      // 初始化临时选择
      tempSelectedApis.value = [...selectedApis.value];
      // 延迟设置表格选中状态，确保数据加载完成
      setTimeout(setTableSelection, 500);
    },
    // onClose: 对话框关闭时的回调
    undefined,
    // onConfirm: 确认时的回调
    () => {
      confirmApiSelection();
    },
  );
</script>

<template>
  <div class="min-h-[600px] bg-gray-50 p-5">
    <!-- 页面标题 -->
    <div class="mb-6">
      <h3 class="m-0 mb-2 text-lg font-semibold text-gray-800">
        选择要添加的API端点
      </h3>
      <p class="m-0 text-sm text-gray-500">
        选择的每个API端点都将成为MCP服务器中的一个工具
      </p>
    </div>

    <!-- 选择API按钮 -->
    <div class="mb-6 flex items-center gap-4">
      <el-button type="primary" :icon="Plus" @click="openDialog">
        选择API
      </el-button>
      <span v-if="selectedCount > 0" class="text-sm text-gray-500">
        已选择 {{ selectedCount }} 个API
      </span>
    </div>

    <!-- 已选择的API表格 -->
    <div
      v-if="selectedCount > 0"
      class="mb-6 rounded-lg bg-white p-6 shadow-sm"
    >
      <div class="mb-4 flex items-center justify-between">
        <h4 class="m-0 text-base font-semibold text-gray-800">已选择的API</h4>
        <span class="text-sm text-gray-500">共 {{ selectedCount }} 个</span>
      </div>

      <el-table :data="selectedApis" border stripe class="w-full">
        <el-table-column prop="apiMethod" label="请求方式" width="100">
          <template #default="{ row }">
            <ElTag :type="getMethodTagType(row.apiMethod)" size="small">
              {{ row.apiMethod }}
            </ElTag>
          </template>
        </el-table-column>
        <el-table-column
          prop="apiUrl"
          label="API路径"
          min-width="200"
          show-overflow-tooltip
        />
        <el-table-column
          prop="apiName"
          label="API名称"
          min-width="150"
          show-overflow-tooltip
        />
        <el-table-column
          prop="apiService"
          label="服务分组"
          width="120"
          show-overflow-tooltip
        />
        <el-table-column label="操作" width="80" align="center">
          <template #default="{ row }">
            <el-button
              type="danger"
              :icon="Delete"
              size="small"
              circle
              @click="removeSelectedApi(row)"
              title="删除"
            />
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 空状态 -->
    <div
      v-else
      class="rounded-lg border-2 border-dashed border-gray-300 bg-gray-50 p-8"
    >
      <div class="flex h-64 flex-col items-center justify-center">
        <div class="mb-4 text-6xl text-gray-400">
          <InfoFilled />
        </div>
        <div class="mb-2 text-lg font-medium text-gray-500">暂未选择API</div>
        <div class="mb-4 text-sm text-gray-400">
          点击"选择API"按钮开始选择要添加的API端点
        </div>
        <el-button type="primary" :icon="Plus" @click="openDialog">
          选择API
        </el-button>
      </div>
    </div>

    <!-- 选择API对话框 -->
    <el-dialog
      v-model="dialogVisible"
      title="选择API"
      width="80%"
      :close-on-click-modal="false"
      destroy-on-close
    >
      <!-- 对话框内容 - VxeGrid表格 -->
      <div class="dialog-content">
        <div class="vp-raw h-[500px] w-full">
          <Grid
            :grid-events="{
              checkboxChange: handleDialogCheckboxChange,
              checkboxAll: handleDialogCheckboxChange,
              proxyQuery: handleProxyQuery,
            }"
          >
            <!-- 请求方式列插槽 -->
            <template #apiMethod="{ row }">
              <ElTag :type="getMethodTagType(row.apiMethod)" size="small">
                {{ row.apiMethod }}
              </ElTag>
            </template>

            <!-- 自定义空状态插槽 -->
            <template #empty>
              <div
                class="flex h-full flex-col items-center justify-center py-16"
              >
                <div class="mb-4 text-6xl text-gray-400">
                  <InfoFilled />
                </div>
                <div class="mb-2 text-lg font-medium text-gray-500">
                  暂无数据
                </div>
                <div class="text-sm text-gray-400">
                  请尝试调整搜索条件或稍后再试
                </div>
              </div>
            </template>
          </Grid>
        </div>
      </div>

      <!-- 对话框底部按钮 -->
      <template #footer>
        <div class="flex items-center justify-end gap-3">
          <span class="mr-4 text-sm text-gray-500">
            已选择 {{ tempSelectedApis.length }} 个API
          </span>
          <el-button @click="cancelDialog">取消</el-button>
          <el-button type="primary" @click="confirmDialog">确定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 提示信息 -->
    <div
      class="mt-6 flex items-start gap-3 rounded-lg border border-blue-200 bg-blue-50 p-4"
    >
      <div class="text-blue-500">
        <InfoFilled class="h-5 w-5" />
      </div>
      <div class="flex-1">
        <div class="text-sm font-medium text-blue-800">提示</div>
        <div class="mt-1 text-sm text-blue-700">
          选择的每个API端点都将成为MCP服务器中的一个工具（Tool）。建议选择功能相关的API组成完整的服务能力。
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* 使用 Tailwind CSS，只保留必要的深度样式 */

/* 对话框样式优化 */
:deep(.el-dialog) {
  border-radius: 12px;
}

:deep(.el-dialog__header) {
  padding: 20px 24px 16px;
  border-bottom: 1px solid #e5e7eb;
}

:deep(.el-dialog__body) {
  padding: 20px 24px;
}

:deep(.el-dialog__footer) {
  padding: 16px 24px 20px;
  border-top: 1px solid #e5e7eb;
}

/* 表格样式优化 */
:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.el-table th) {
  background-color: #f1f5f9;
  color: #374151;
  font-weight: 600;
}

:deep(.el-table td) {
  border-bottom: 1px solid #f1f5f9;
}

:deep(.el-table tr:hover > td) {
  background-color: #f8fafc;
}
</style>
