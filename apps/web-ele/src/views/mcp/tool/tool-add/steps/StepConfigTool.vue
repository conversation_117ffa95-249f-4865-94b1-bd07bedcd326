<script lang="ts" setup>
import { watch } from 'vue';

import { InfoFilled } from '@element-plus/icons-vue';
import { ElTag } from 'element-plus';

import { useVbenForm } from '#/adapter/form';

import { useToolFormConfig } from '../composables/useToolFormConfig';
import { useToolWizard } from '../composables/useToolWizard';
import { getMethodTagType } from '../utils/apiUtils';

const { toolConfigs } = useToolWizard();
const { createFormOptions } = useToolFormConfig();

// 为每个工具创建表单实例
const toolForms: Array<{ config: any; Form: any; formApi: any }> = [];

// 创建所有表单实例
const createAllForms = () => {
  // 清空现有表单
  toolForms.length = 0;

  // 为每个工具配置创建表单
  toolConfigs.value.forEach((config: any) => {
    const formOptions = createFormOptions();
    const [Form, formApi] = useVbenForm(formOptions);

    // 设置初始值
    const initialValues = {
      toolName: config.toolName || '',
      displayName: config.displayName || '',
      toolDescription: config.toolDescription || '',
      schemaContent: config.schema
        ? JSON.stringify(config.schema, null, 2)
        : '',
    };

    // 延迟设置初始值
    setTimeout(() => {
      formApi.setValues(initialValues);
    }, 100);

    toolForms.push({ config, Form, formApi });
  });
};

// 监听工具配置变化
watch(
  toolConfigs,
  (newConfigs) => {
    if (newConfigs?.length > 0) {
      createAllForms();
    }
  },
  { immediate: true, deep: true },
);

// 验证所有工具配置
const validateAllTools = async (): Promise<boolean> => {
  try {
    for (const { formApi } of toolForms) {
      const isValid = await formApi.validate();
      if (!isValid) {
        return false;
      }
    }
    return true;
  } catch {
    return false;
  }
};

// 暴露验证方法
defineExpose({
  validateAllTools,
});
</script>

<template>
  <div class="min-h-[600px] bg-gray-50 p-5">
    <!-- 页面标题 -->
    <div class="mb-6">
      <h3 class="m-0 mb-2 text-lg font-semibold text-gray-800">配置工具定义</h3>
      <p class="m-0 text-sm text-gray-500">
        为每个API端点配置工具信息和参数Schema
      </p>
    </div>

    <!-- 工具列表 -->
    <div
      v-if="toolForms.length > 0"
      class="max-h-[800px] space-y-6 overflow-y-auto"
    >
      <div
        v-for="(toolForm, index) in toolForms"
        :key="toolForm.config.apiId"
        class="rounded-lg bg-white shadow-sm"
      >
        <!-- 工具标题 -->
        <div class="border-b border-gray-200 p-4">
          <div class="flex items-center gap-3">
            <div
              class="flex h-8 w-8 items-center justify-center rounded-full bg-blue-100"
            >
              <span class="text-sm font-semibold text-blue-600">{{
                index + 1
              }}</span>
            </div>
            <div class="flex-1">
              <h4 class="m-0 text-base font-semibold text-gray-800">
                工具{{ index + 1 }}:
                {{ toolForm.config.displayName || toolForm.config.toolName }}
              </h4>
              <div class="mt-1 flex items-center gap-4">
                <div class="flex items-center gap-2">
                  <ElTag
                    :type="getMethodTagType(toolForm.config.method)"
                    size="small"
                  >
                    {{ toolForm.config.method }}
                  </ElTag>
                  <code
                    class="rounded bg-gray-100 px-2 py-1 font-mono text-xs text-gray-600"
                  >
                    {{ toolForm.config.path }}
                  </code>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 工具表单 -->
        <div class="p-6">
          <component :is="toolForm.Form" />
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-else class="flex h-64 items-center justify-center">
      <div class="text-center">
        <InfoFilled class="mx-auto mb-4 h-12 w-12 text-gray-400" />
        <div class="mb-2 text-lg font-medium text-gray-600">暂无工具配置</div>
        <div class="text-sm text-gray-500">请先在上一步选择API端点</div>
      </div>
    </div>
  </div>
</template>
<style scoped>
/* Schema 编辑器特殊样式 - 保留复杂的深度样式 */
:deep(.schema-textarea) {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace !important;
  font-size: 13px !important;
  line-height: 1.5 !important;
  background: #1f2937 !important;
  color: #f9fafb !important;
  border: 1px solid #374151 !important;
  border-radius: 6px !important;
  padding: 16px !important;
  min-height: 300px !important;
  resize: vertical !important;
}

:deep(.schema-textarea:focus) {
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
  outline: none !important;
}

:deep(.schema-textarea::placeholder) {
  color: #9ca3af !important;
}

/* 响应式设计 - 保留复杂的媒体查询 */
@media (max-width: 768px) {
  :deep(.schema-textarea) {
    min-height: 250px !important;
  }
}
</style>
