<script lang="ts" setup>
import { useToolWizard } from '../composables/useToolWizard';

const {
  toolConfigs,
  paramTransformers,
  addParameterTransformer,
  removeParameterTransformer,
} = useToolWizard();

const transformRuleOptions = [
  { label: '使用内置时间范围转换器', value: 'builtin_time' },
  { label: '自定义映射表', value: 'mapping' },
  { label: '自定义转换函数', value: 'custom' },
  { label: '无转换', value: 'none' },
];

const onTransformRuleChange = (row: any) => {
  // 如果转换规则不是映射表或自定义函数，清空对应字段
  if (row.transformRule !== 'mapping') {
    row.mappingTable = '';
  }
  if (row.transformRule !== 'custom') {
    row.customFunction = '';
  }
};
</script>

<template>
  <div class="param-transform">
    <div v-if="toolConfigs.length === 0" class="empty-tip">
      请先选择并配置工具。
    </div>

    <div v-for="tool in toolConfigs" :key="tool.apiId" class="transform-card">
      <h3 class="tool-title">{{ tool.displayName }} 参数转换</h3>

      <el-table
        :data="paramTransformers[tool.apiId]"
        style="width: 100%"
        size="small"
        border
      >
        <el-table-column
          prop="aiParam"
          label="AI 参数名"
          width="120"
          :editable="true"
        >
          <template #default="{ row }">
            <el-input
              v-model="row.aiParam"
              placeholder="AI参数名"
              size="small"
            />
          </template>
        </el-table-column>

        <el-table-column
          prop="apiParam"
          label="API 参数名"
          width="120"
          :editable="true"
        >
          <template #default="{ row }">
            <el-input
              v-model="row.apiParam"
              placeholder="API参数名"
              size="small"
            />
          </template>
        </el-table-column>

        <el-table-column prop="transformRule" label="转换规则" width="180">
          <template #default="{ row }">
            <el-select
              v-model="row.transformRule"
              placeholder="请选择转换规则"
              size="small"
              clearable
              @change="onTransformRuleChange(row)"
            >
              <el-option
                v-for="option in transformRuleOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </template>
        </el-table-column>

        <el-table-column prop="mappingTable" label="映射表" width="180">
          <template #default="{ row }">
            <el-input
              v-if="row.transformRule === 'mapping'"
              v-model="row.mappingTable"
              placeholder="输入映射表名"
              size="small"
            />
          </template>
        </el-table-column>

        <el-table-column prop="customFunction" label="自定义函数">
          <template #default="{ row }">
            <el-input
              v-if="row.transformRule === 'custom'"
              type="textarea"
              v-model="row.customFunction"
              :rows="6"
              placeholder="输入自定义转换函数代码"
              size="small"
              style="font-family: Consolas, monospace"
            />
          </template>
        </el-table-column>

        <el-table-column label="操作" width="100">
          <template #default="{ $index }">
            <el-button
              type="danger"
              size="mini"
              icon="el-icon-delete"
              circle
              @click="removeParameterTransformer(tool.apiId, $index)"
              :disabled="(paramTransformers[tool.apiId]?.length || 0) <= 1"
              title="删除"
            />
          </template>
        </el-table-column>
      </el-table>

      <el-button
        type="primary"
        size="small"
        icon="el-icon-plus"
        class="add-transform-btn"
        @click="addParameterTransformer(tool.apiId)"
      >
        新增参数转换
      </el-button>
    </div>
  </div>
</template>

<style scoped>
.param-transform {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.transform-card {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 20px;
  background: #f9fafb;
}

.tool-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 12px;
}

.add-transform-btn {
  margin-top: 12px;
}

.el-table .cell {
  padding: 8px 12px;
}

.el-input textarea {
  font-family: 'Consolas', monospace;
  font-size: 13px;
}
</style>
