<!--
 * @Author: z<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-07-14 15:11:40
 * @LastEditors: zhangqian <EMAIL>
 * @LastEditTime: 2025-07-29 17:38:42
 * @Description: mcp管理中心-工具管理-添加API工具页面 （目前使用）
 * @FilePath: /dev-platform-ui/apps/web-ele/src/views/mcp/tool/tool-add/index.vue
-->
<script setup lang="ts">
import { computed, onMounted, ref } from 'vue';

import { Page } from '@vben/common-ui';

import { ElMessage } from 'element-plus';

import {
  resetToolWizardState,
  useToolWizard,
} from './composables/useToolWizard';
import StepConfigTool from './steps/StepConfigTool.vue';
import StepSelectApi from './steps/StepSelectApi.vue';
import StepTestAndFinish from './steps/StepTestAndFinish.vue';

// 使用工具向导状态
const { canProceedToNext, saveToolConfiguration } = useToolWizard();

// 在组件挂载时重置状态，避免状态残留
onMounted(() => {
  resetToolWizardState();
});

// 当前步骤
const currentStep = ref(0);

// 步骤组件引用
const stepConfigToolRef = ref<InstanceType<typeof StepConfigTool>>();

// 步骤定义
const steps = [
  {
    title: '选择API',
    description: '从已有API列表中选择需要转换的端点',
    value: 'selectApi',
  },
  {
    title: '配置工具',
    description: '为API设置AI友好的调用名称和描述',
    value: 'configTool',
  },
  // {
  //   title: '参数转换',
  //   description: '配置自然语言参数到API参数的映射',
  //   value: 'paramTransform',
  // },
  {
    title: '测试与完成',
    description: '测试工具并完成配置',
    value: 'testAndFinish',
  },
];

// 计算下一步按钮文字
const nextButtonText = computed(() => {
  if (currentStep.value === steps.length - 1) {
    return '完成配置';
  }

  const nextStepIndex = currentStep.value + 1;
  if (nextStepIndex < steps.length && steps[nextStepIndex]) {
    return `下一步：${steps[nextStepIndex].title}`;
  }

  return '下一步';
});

// 步骤切换
const goToStep = async (step: number) => {
  if (step < 0 || step >= steps.length) return;

  // 验证当前步骤是否可以离开
  if (step > currentStep.value) {
    const canProceed = await canProceedFromCurrentStep();
    if (!canProceed) {
      return;
    }
  }

  currentStep.value = step;
};

// 检查当前步骤是否可以继续
const canProceedFromCurrentStep = async (): Promise<boolean> => {
  switch (currentStep.value) {
    case 0: {
      // 选择API
      if (!canProceedToNext.value) {
        ElMessage.warning('请至少选择一个API端点');
        return false;
      }
      return true;
    }
    case 1: {
      // 配置工具
      if (!stepConfigToolRef.value) {
        ElMessage.error('表单组件未初始化');
        return false;
      }

      try {
        const isValid = await stepConfigToolRef.value.validateAllTools();
        if (!isValid) {
          ElMessage.warning('请完善工具配置信息');
        }
        return isValid;
      } catch (error) {
        console.error('表单验证失败:', error);
        ElMessage.error('表单验证失败');
        return false;
      }
    }
    case 2: {
      return true;
    }
    default: {
      return true;
    }
  }
};

// 下一步
const nextStep = async () => {
  const canProceed = await canProceedFromCurrentStep();

  if (canProceed) {
    if (currentStep.value === steps.length - 1) {
      // 最后一步，保存配置
      saveToolConfiguration();
    } else {
      await goToStep(currentStep.value + 1);
    }
  }
};

// 上一步
const previousStep = () => {
  goToStep(currentStep.value - 1);
};
</script>

<template>
  <Page title="添加API工具" description="将REST API端点配置为AI可调用的工具">
    <!-- 步骤导航 -->
    <div class="mb-6 rounded-lg border border-gray-200 bg-white p-6 shadow-sm">
      <el-steps :active="currentStep" finish-status="success" align-center>
        <el-step
          v-for="(step, index) in steps"
          :key="index"
          @click="goToStep(index)"
        >
          <template #icon>
            <div
              class="flex h-8 w-8 cursor-pointer items-center justify-center rounded-full border-2 text-sm font-semibold transition-all duration-300"
              :class="{
                'border-blue-500 bg-blue-500 text-white': currentStep === index,
                'border-green-500 bg-green-500 text-white': currentStep > index,
                'border-gray-300 bg-gray-100 text-gray-500':
                  currentStep < index,
              }"
            >
              {{ index + 1 }}
            </div>
          </template>
          <template #title>
            <div
              class="mt-2 cursor-pointer text-sm font-medium transition-colors duration-300"
              :class="{
                'text-blue-600': currentStep === index,
                'text-green-600': currentStep > index,
                'text-gray-500': currentStep < index,
              }"
            >
              {{ step.title }}
            </div>
          </template>
          <template #description>
            <div
              class="mt-1 cursor-pointer text-xs transition-colors duration-300"
              :class="{
                'text-blue-500': currentStep === index,
                'text-green-500': currentStep > index,
                'text-gray-400': currentStep < index,
              }"
            >
              {{ step.description }}
            </div>
          </template>
        </el-step>
      </el-steps>
    </div>

    <!-- 步骤内容 -->
    <div class="step-content">
      <!-- 步骤1：选择API -->
      <StepSelectApi v-if="currentStep === 0" />

      <!-- 步骤2：配置工具 -->
      <StepConfigTool v-if="currentStep === 1" ref="stepConfigToolRef" />

      <!-- 步骤3：参数转换 -->
      <!-- <StepParamTransform v-if="currentStep === 2" /> -->

      <!-- 步骤4：测试与完成 -->
      <StepTestAndFinish v-if="currentStep === 2" />

      <!-- 步骤导航按钮 -->
      <div
        class="mt-6 flex items-center justify-between rounded-lg border border-gray-200 bg-white p-4 shadow-sm"
      >
        <el-button @click="previousStep" :disabled="currentStep === 0">
          上一步
        </el-button>
        <el-button type="primary" @click="nextStep">
          {{ nextButtonText }}
        </el-button>
      </div>
    </div>
  </Page>
</template>
