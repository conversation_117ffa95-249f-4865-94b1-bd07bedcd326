/*
 * @Author: <PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-07-18 17:10:44
 * @LastEditors: zhang<PERSON>an <EMAIL>
 * @LastEditTime: 2025-07-21 14:18:50
 * @Description: 页面功能描述，例如：用户列表、商品详情等
 * @FilePath: /dev-platform-ui/apps/web-ele/src/views/mcp/tool/tool-add/utils/apiUtils.ts
 */
import type { ApiGroup } from '../types';

export const mockApiGroups: ApiGroup[] = [
  {
    groupName: '订单管理API',
    endpoints: [
      {
        id: 'ep_order_list',
        apiMethod: 'GET',
        apiUrl: '/api/v2/orders',
        apiName: 'listOrders',
        apiService: 'OrderService',
        description: '获取订单列表',
      },
      {
        id: 'ep_order_detail',
        apiMethod: 'GET',
        apiUrl: '/api/v2/orders/{orderId}',
        apiName: 'getOrderDetail',
        apiService: 'OrderService',
        description: '获取订单详情',
      },
      {
        id: 'ep_order_create',
        apiMethod: 'POST',
        apiUrl: '/api/v2/orders',
        apiName: 'createOrder',
        apiService: 'OrderService',
        description: '创建新订单',
      },
      {
        id: 'ep_order_update_status',
        apiMethod: 'PUT',
        apiUrl: '/api/v2/orders/{orderId}/status',
        apiName: 'updateOrderStatus',
        apiService: 'OrderService',
        description: '更新订单状态',
      },
      {
        id: 'ep_order_cancel',
        apiMethod: 'DELETE',
        apiUrl: '/api/v2/orders/{orderId}',
        apiName: 'cancelOrder',
        apiService: 'OrderService',
        description: '取消订单',
      },
    ],
  },
  {
    groupName: '商品管理API',
    endpoints: [
      {
        id: 'ep_product_search',
        apiMethod: 'GET',
        apiUrl: '/api/v2/products/search',
        apiName: 'searchProducts',
        apiService: 'ProductService',
        description: '搜索商品',
      },
      {
        id: 'ep_product_detail',
        apiMethod: 'GET',
        apiUrl: '/api/v2/products/{productId}',
        apiName: 'getProductDetail',
        apiService: 'ProductService',
        description: '获取商品详情',
      },
      {
        id: 'ep_product_inventory',
        apiMethod: 'GET',
        apiUrl: '/api/v2/products/{productId}/inventory',
        apiName: 'getProductInventory',
        apiService: 'ProductService',
        description: '查询库存',
      },
    ],
  },
  {
    groupName: '用户管理API',
    endpoints: [
      {
        id: 'ep_user_profile',
        apiMethod: 'GET',
        apiUrl: '/api/v2/users/{userId}',
        apiName: 'getUserProfile',
        apiService: 'UserService',
        description: '获取用户信息',
      },
      {
        id: 'ep_user_update',
        apiMethod: 'PUT',
        apiUrl: '/api/v2/users/{userId}',
        apiName: 'updateUser',
        apiService: 'UserService',
        description: '更新用户信息',
      },
    ],
  },
];

export const fetchMockApiGroups = async (): Promise<ApiGroup[]> => {
  return new Promise((resolve) =>
    setTimeout(() => resolve(mockApiGroups), 800),
  );
};

// 智能生成工具名称
export const generateToolName = (method: string, path: string): string => {
  const methodMap: Record<string, string> = {
    GET: 'get',
    POST: 'create',
    PUT: 'update',
    DELETE: 'delete',
  };

  const verb = methodMap[method] || method.toLowerCase();

  const parts = path.split('/').filter((p) => p && !p.startsWith('{'));
  const resource = parts[parts.length - 1] || parts[parts.length - 2] || 'Api';

  // 驼峰化处理 foo-bar -> fooBar
  const camel = resource.replaceAll(/-([a-z])/g, (_, c) => c.toUpperCase());
  const pascal = camel.charAt(0).toUpperCase() + camel.slice(1);

  return verb + pascal;
};

// 获取方法标签类型
export const getMethodTagType = (
  method: string,
): 'danger' | 'info' | 'primary' | 'success' | 'warning' => {
  const map: Record<string, 'danger' | 'primary' | 'success' | 'warning'> = {
    GET: 'primary',
    POST: 'success',
    PUT: 'warning',
    DELETE: 'danger',
  };
  return map[method] || 'info';
};

// 获取状态标签类型
export const getStatusTagType = (
  status: string,
): 'danger' | 'info' | 'success' | 'warning' => {
  const typeMap: Record<string, 'danger' | 'info' | 'success' | 'warning'> = {
    enabled: 'success',
    disabled: 'danger',
    maintenance: 'warning',
  };
  return typeMap[status] || 'info';
};
