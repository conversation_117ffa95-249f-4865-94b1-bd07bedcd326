export interface ApiEndpoint {
  id: string;
  apiMethod: string;
  apiUrl: string;
  apiName: string;
  apiService: string;
  description?: string;
}

export interface ApiGroup {
  groupName: string;
  endpoints: ApiEndpoint[];
}

export interface ToolConfig {
  apiId: string;
  method: string;
  path: string;
  description: string;
  toolName: string;
  displayName: string;
  toolDescription: string;
  schema?: any; // JSON Schema 配置
}

// 分页查询参数
export interface PageQueryParams {
  currentPage: number;
  pageSize: number;
  searchKey?: string;
}

// 分页查询响应
export interface PageQueryResponse<T> {
  data: T[];
  total: number;
  totalCount?: number;
}

export interface ParamTransformer {
  aiParam: string;
  apiParam: string;
  transformRule: 'builtin_time' | 'custom' | 'mapping' | 'none';
  mappingTable?: string;
  customFunction?: string;
  description?: string;
}

export interface WizardStep {
  title: string;
  description: string;
  value: string;
}
