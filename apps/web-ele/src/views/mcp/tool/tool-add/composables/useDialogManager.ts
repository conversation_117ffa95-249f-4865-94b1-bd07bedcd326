/*
 * @Author: <PERSON><PERSON><PERSON><PERSON> zhang<PERSON><EMAIL>
 * @Date: 2025-07-29 17:30:34
 * @LastEditors: z<PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-07-29 17:33:20
 * @Description: 页面功能描述，例如：用户列表、商品详情等
 * @FilePath: /dev-platform-ui/apps/web-ele/src/views/mcp/tool/tool-add/composables/useDialogManager.ts
 */
import { ref, watch } from 'vue';

/**
 * 对话框管理 Composable
 * 负责管理对话框的显示/隐藏状态和相关操作
 */
export function useDialogManager(
  onOpen?: () => void,
  onClose?: () => void,
  onConfirm?: () => Promise<void> | void,
) {
  // 对话框显示状态
  const dialogVisible = ref(false);
  const loading = ref(false);

  // 打开对话框
  const openDialog = () => {
    dialogVisible.value = true;
    onOpen?.();
  };

  // 关闭对话框
  const closeDialog = () => {
    dialogVisible.value = false;
    onClose?.();
  };

  // 确认操作
  const confirmDialog = async () => {
    if (loading.value) return;

    try {
      loading.value = true;
      await onConfirm?.();
      closeDialog();
    } catch {
      // 错误处理由具体的 onConfirm 函数负责
    } finally {
      loading.value = false;
    }
  };

  // 取消操作
  const cancelDialog = () => {
    closeDialog();
  };

  // 监听对话框状态变化
  watch(dialogVisible, (newVisible) => {
    if (newVisible) {
      onOpen?.();
    } else {
      onClose?.();
    }
  });

  return {
    dialogVisible,
    loading,
    openDialog,
    closeDialog,
    confirmDialog,
    cancelDialog,
  };
}
