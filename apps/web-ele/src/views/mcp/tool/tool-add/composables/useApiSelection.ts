import type { Ref } from 'vue';

import type { ApiEndpoint } from '../types';

import { computed } from 'vue';

import { ElMessage, ElMessageBox } from 'element-plus';

/**
 * API 选择管理 Composable
 * 负责管理已选择的 API 列表，包括添加、删除、确认等操作
 */
export function useApiSelection(
  selectedApis: Ref<ApiEndpoint[]>,
  tempSelectedApis: Ref<ApiEndpoint[]>,
  confirmApiSelection: () => void,
) {
  // 计算已选择API的数量
  const selectedCount = computed(() => selectedApis.value.length);

  // 初始化临时选择（用于对话框）
  const initTempSelection = () => {
    tempSelectedApis.value = [...selectedApis.value];
  };

  // 确认选择
  const confirmSelection = () => {
    confirmApiSelection();
    ElMessage.success(`已选择 ${selectedApis.value.length} 个API`);
  };

  // 删除已选择的API
  const removeSelectedApi = async (api: ApiEndpoint) => {
    try {
      await ElMessageBox.confirm(
        `确定要删除 "${api.apiName}" 吗？`,
        '确认删除',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        },
      );

      const index = selectedApis.value.findIndex((item) => item.id === api.id);
      if (index !== -1) {
        selectedApis.value.splice(index, 1);
        ElMessage.success('删除成功');
      }
    } catch {
      // 用户取消删除
    }
  };

  // 批量删除选中的API
  const removeMultipleApis = async (apiIds: string[]) => {
    try {
      await ElMessageBox.confirm(
        `确定要删除选中的 ${apiIds.length} 个API吗？`,
        '批量删除',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        },
      );

      selectedApis.value = selectedApis.value.filter(
        (api) => !apiIds.includes(api.id),
      );
      ElMessage.success(`已删除 ${apiIds.length} 个API`);
    } catch {
      // 用户取消删除
    }
  };

  // 清空所有选择
  const clearAllSelection = async () => {
    if (selectedApis.value.length === 0) {
      ElMessage.warning('没有可清空的API');
      return;
    }

    try {
      await ElMessageBox.confirm('确定要清空所有已选择的API吗？', '清空选择', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      });

      selectedApis.value = [];
      ElMessage.success('已清空所有选择');
    } catch {
      // 用户取消清空
    }
  };

  // 检查API是否已被选择
  const isApiSelected = (apiId: string) => {
    return selectedApis.value.some((api) => api.id === apiId);
  };

  return {
    selectedCount,
    initTempSelection,
    confirmSelection,
    removeSelectedApi,
    removeMultipleApis,
    clearAllSelection,
    isApiSelected,
  };
}
