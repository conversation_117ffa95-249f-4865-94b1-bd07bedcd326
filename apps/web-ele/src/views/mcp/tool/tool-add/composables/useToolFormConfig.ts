import type { VbenFormProps } from '@vben/common-ui';

import { z } from '#/adapter/form';

/**
 * 工具配置表单配置
 * 负责定义表单字段、验证规则和组件配置
 */
export function useToolFormConfig() {
  // 表单配置
  const createFormOptions = (): VbenFormProps => ({
    submitOnEnter: false,
    showDefaultActions: false, // 不显示默认的提交和重置按钮
    commonConfig: {
      labelWidth: 120, // 注意：labelWidth 是 number 类型，单位是 px
    },
    schema: [
      {
        component: 'Input',
        componentProps: {
          placeholder: '请输入工具调用名称（英文函数名）',
          clearable: true,
        },
        fieldName: 'toolName',
        label: '工具调用名称',
        rules: z
          .string()
          .min(2, '工具名称长度应在2-50个字符之间')
          .max(50, '工具名称长度应在2-50个字符之间')
          .regex(
            /^[a-z]\w*$/i,
            '工具名称必须以字母开头，只能包含字母、数字和下划线',
          ),
        help: '用于AI调用的函数名，如：listOrders、getUserInfo',
      },
      {
        component: 'Input',
        componentProps: {
          placeholder: '请输入工具显示名称（中文名称）',
          clearable: true,
        },
        fieldName: 'displayName',
        label: '工具显示名称',
        rules: z
          .string()
          .min(2, '显示名称长度应在2-20个字符之间')
          .max(20, '显示名称长度应在2-20个字符之间'),
        help: '用户界面显示的中文名称',
      },
      {
        component: 'Input',
        componentProps: {
          type: 'textarea',
          rows: 4,
          placeholder: '请详细描述工具的功能和使用场景...',
          maxlength: 500,
          showWordLimit: true,
        },
        fieldName: 'toolDescription',
        label: '工具描述',
        rules: z
          .string()
          .min(10, '工具描述长度应在10-500个字符之间')
          .max(500, '工具描述长度应在10-500个字符之间'),
        help: '详细描述工具功能、使用场景和注意事项，帮助AI更好地理解和使用',
      },
      {
        component: 'Input',
        componentProps: {
          type: 'textarea',
          rows: 12,
          placeholder: '请输入JSON Schema配置...',
          class: 'schema-textarea',
        },
        fieldName: 'schemaContent',
        label: 'Schema配置',
        rules: z
          .string()
          .min(1, '请输入Schema配置')
          .refine(
            (value) => {
              try {
                JSON.parse(value);
                return true;
              } catch {
                return false;
              }
            },
            { message: 'Schema格式错误，请检查JSON语法' },
          ),
        help: '定义API参数的数据结构，支持JSON Schema格式',
      },
    ],
  });

  // 验证 JSON Schema 格式
  const validateJsonSchema = (value: string): boolean => {
    try {
      JSON.parse(value);
      return true;
    } catch {
      return false;
    }
  };

  // 格式化 Schema 内容
  const formatSchemaContent = (schema: any): string => {
    try {
      return JSON.stringify(schema, null, 2);
    } catch {
      return '{}';
    }
  };

  return {
    createFormOptions,
    validateJsonSchema,
    formatSchemaContent,
  };
}
