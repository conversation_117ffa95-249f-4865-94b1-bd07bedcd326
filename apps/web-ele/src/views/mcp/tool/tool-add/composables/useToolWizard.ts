import type { ApiEndpoint, ParamTransformer, ToolConfig } from '../types';

import type { VxeGridProps } from '#/adapter/vxe-table';

import { computed, reactive, ref, watch } from 'vue';

import { ElMessage } from 'element-plus';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { pageQueryApiApi } from '#/api/mcp';

import { generateToolName } from '../utils/apiUtils';

// 默认的 Schema 模板
export const defaultSchema = {
  type: 'object',
  properties: {
    startDate: {
      type: 'string',
      format: 'date',
      description: '查询开始日期，格式：YYYY-MM-DD',
    },
    endDate: {
      type: 'string',
      format: 'date',
      description: '查询结束日期，格式：YYYY-MM-DD',
    },
  },
  required: ['startDate', 'endDate'],
};

// Schema 模板类型定义
interface SchemaProperty {
  type: string;
  description: string;
  minimum?: number;
  maximum?: number;
  format?: string;
  default?: any;
}

interface SchemaTemplate {
  type: 'object';
  properties: Record<string, SchemaProperty>;
  required: string[];
}

// Schema 模板定义
const SCHEMA_TEMPLATES: Record<string, SchemaTemplate> = {
  list: {
    type: 'object',
    properties: {
      page: {
        type: 'integer',
        minimum: 1,
        description: '页码，从1开始',
        default: 1,
      },
      pageSize: {
        type: 'integer',
        minimum: 1,
        maximum: 100,
        description: '每页数量，最大100',
        default: 10,
      },
      keyword: {
        type: 'string',
        description: '搜索关键词',
      },
    },
    required: [],
  },
  detail: {
    type: 'object',
    properties: {
      id: {
        type: 'string',
        description: '记录ID',
      },
    },
    required: ['id'],
  },
  create: {
    type: 'object',
    properties: {
      name: {
        type: 'string',
        description: '名称',
      },
      description: {
        type: 'string',
        description: '描述',
      },
    },
    required: ['name'],
  },
  update: {
    type: 'object',
    properties: {
      id: {
        type: 'string',
        description: '要更新的记录ID',
      },
      name: {
        type: 'string',
        description: '名称',
      },
      description: {
        type: 'string',
        description: '描述',
      },
    },
    required: ['id'],
  },
  delete: {
    type: 'object',
    properties: {
      id: {
        type: 'string',
        description: '要删除的记录ID',
      },
    },
    required: ['id'],
  },
} as const;

// 根据 API 特点生成智能 Schema
export const generateSmartSchema = (endpoint: ApiEndpoint) => {
  const method = endpoint.apiMethod.toUpperCase();
  const path = endpoint.apiUrl.toLowerCase();
  const name = (endpoint.apiName || '').toLowerCase();

  // 检查关键词的辅助函数
  const hasKeywords = (keywords: string[]) =>
    keywords.some(
      (keyword) => path.includes(keyword) || name.includes(keyword),
    );

  // 根据 HTTP 方法和路径特征选择模板
  switch (method) {
    case 'DELETE': {
      return SCHEMA_TEMPLATES.delete;
    }

    case 'GET': {
      if (hasKeywords(['list', 'search', '查询', '列表'])) {
        return SCHEMA_TEMPLATES.list;
      }
      if (hasKeywords(['detail', 'info', '详情', '信息'])) {
        return SCHEMA_TEMPLATES.detail;
      }
      break;
    }

    case 'PATCH':
    case 'PUT': {
      return SCHEMA_TEMPLATES.update;
    }

    case 'POST': {
      if (hasKeywords(['create', 'add', '创建', '新增'])) {
        return SCHEMA_TEMPLATES.create;
      }
      break;
    }
  }

  // 默认返回通用 Schema
  return defaultSchema;
};

// 全局状态 - 确保所有组件共享同一个状态实例
const globalState = {
  // Step 1: API 选择
  selectedApis: ref<ApiEndpoint[]>([]), // 保存完整的已选择API数据
  tempSelectedApis: ref<ApiEndpoint[]>([]), // 对话框中的临时选择

  // Step 2: 工具配置
  toolConfigs: ref<ToolConfig[]>([]),

  // Step 3: 参数转换器
  paramTransformers: ref<Record<string, ParamTransformer[]>>({}),

  // Step 4: 测试
  testQuery: ref(''),
  testLoading: ref(false),
  testResults: ref({
    visible: false,
    success: false,
    aiUnderstanding: '',
    parameterTransform: '',
    apiResponse: '',
  }),

  // 完成信息
  finishForm: reactive({
    toolGroup: '',
    description: '',
  }),
};

// 重置状态函数
export function resetToolWizardState() {
  globalState.selectedApis.value = [];
  globalState.tempSelectedApis.value = [];
  globalState.toolConfigs.value = [];
  globalState.paramTransformers.value = {};
  globalState.testQuery.value = '';
  globalState.testLoading.value = false;
  globalState.testResults.value = {
    visible: false,
    success: false,
    aiUnderstanding: '',
    parameterTransform: '',
    apiResponse: '',
  };
  globalState.finishForm.toolGroup = '';
  globalState.finishForm.description = '';
}

// 每次调用useToolWizard会形成一个新的作用域 因此我们抽离出一个全局状态
export function useToolWizard() {
  // 使用全局状态
  const {
    selectedApis,
    tempSelectedApis,
    toolConfigs,
    paramTransformers,
    testQuery,
    testLoading,
    testResults,
    finishForm,
  } = globalState;

  // 测试运行逻辑
  const runTest = async () => {
    if (!testQuery.value.trim()) {
      ElMessage.warning('请输入测试查询');
      return;
    }

    testLoading.value = true;
    try {
      // 模拟调用
      await new Promise((resolve) => setTimeout(resolve, 2000));

      testResults.value = {
        visible: true,
        success: true,
        aiUnderstanding: `选择工具：listOrders\n参数解析：{\n  "timeRange": "最近7天",\n  "orderStatus": "待发货"\n}`,
        parameterTransform: `API参数：{\n  "startDate": "2024-01-09",\n  "endDate": "2024-01-15",\n  "status": "pending_shipment"\n}`,
        apiResponse: `{"success": true, "data": {"total": 15, "orders": [{"id": "ORD-2024-0156", "customer": "张三"}]}}`,
      };

      ElMessage.success('测试成功！');
    } catch {
      testResults.value.success = false;
      ElMessage.error('测试失败');
    } finally {
      testLoading.value = false;
    }
  };

  // 保存配置逻辑
  const saveToolConfiguration = async () => {
    if (toolConfigs.value.length === 0) {
      ElMessage.warning('请至少配置一个工具');
      return;
    }

    try {
      await new Promise((resolve) => setTimeout(resolve, 1000));

      ElMessage.success('工具配置保存成功');
      // 可以跳转或重置逻辑
    } catch {
      ElMessage.error('保存失败');
    }
  };

  // 是否可以进入下一步
  const canProceedToNext = computed(() => selectedApis.value.length > 0);

  // 参数转换器管理
  const addParameterTransformer = (toolId: string) => {
    if (!paramTransformers.value[toolId]) {
      paramTransformers.value[toolId] = [];
    }
    paramTransformers.value[toolId].push(createDefaultTransformer());
  };

  const removeParameterTransformer = (toolId: string, index: number) => {
    const transformers = paramTransformers.value[toolId];
    if (transformers && transformers.length > 1) {
      transformers.splice(index, 1);
    }
  };

  // 生成工具配置的函数
  const generateToolConfigs = () => {
    toolConfigs.value = selectedApis.value
      .map((endpoint) => {
        // 智能生成工具名称
        const suggestedName = generateToolName(
          endpoint.apiMethod,
          endpoint.apiUrl,
        );

        // 生成智能 Schema
        const smartSchema = generateSmartSchema(endpoint);

        return {
          apiId: endpoint.id,
          method: endpoint.apiMethod,
          path: endpoint.apiUrl,
          description: endpoint.description || endpoint.apiName || '',
          toolName: suggestedName,
          displayName: endpoint.apiName || endpoint.description || '',
          toolDescription: `${endpoint.apiName || endpoint.description || ''}，支持通过自然语言调用该API。`,
          schema: smartSchema,
        };
      })
      .filter(Boolean) as ToolConfig[];
  };

  // 表单搜索配置
  const formOptions = {
    collapsed: false,
    showCollapseButton: false,
    submitOnChange: false,
    submitOnEnter: true,
    schema: [
      {
        component: 'Input',
        componentProps: {
          placeholder: '搜索API名称或路径...',
          clearable: true,
        },
        fieldName: 'searchKey',
        label: '搜索',
      },
    ],
    submitButtonOptions: { content: '搜索' },
    resetButtonOptions: { content: '重置' },
  };

  // VxeGrid表格配置
  const gridOptions: VxeGridProps<ApiEndpoint> = {
    height: 500, // 固定高度，适合分页模式
    // keepSource: true,
    // border: true,
    stripe: true,
    showOverflow: true,
    checkboxConfig: {
      highlight: true,
      // labelField: 'apiName',
    },
    columns: [
      {
        align: 'left',
        title: '',
        type: 'checkbox',
        width: 50,
      },
      {
        field: 'apiMethod',
        title: '请求方式',
        width: 100,
        slots: {
          default: 'apiMethod',
        },
      },
      {
        field: 'apiUrl',
        title: 'API路径',
        minWidth: 300,
      },
      {
        field: 'apiName',
        title: 'API名称',
        minWidth: 200,
      },
      {
        field: 'apiService',
        title: '服务分组',
        width: 150,
      },
    ],
    // 启用分页器
    pagerConfig: {
      enabled: true,
      pageSize: 20,
      pageSizes: [10, 20, 50, 100],
      layouts: [
        'PrevPage',
        'JumpNumber',
        'NextPage',
        'FullJump',
        'Sizes',
        'Total',
      ],
    },
    // 禁用虚拟滚动
    scrollY: {
      enabled: false,
    },

    // 启用代理配置，支持分页查询
    proxyConfig: {
      ajax: {
        query: async ({ page }, formValues) => {
          try {
            const { currentPage, pageSize } = page;
            const params = {
              searchKey: formValues?.searchKey || '',
              currentPage,
              pageSize,
            };

            const result = await pageQueryApiApi(params);
            if (result?.data) {
              return {
                items: result.data,
                total: result.totalCount || 0,
              };
            }
            throw new Error('API返回数据格式不正确');
          } catch {
            return { items: [], total: 0 };
          }
        },
      },
    },
  };

  const [Grid, gridApi] = useVbenVxeGrid({ formOptions, gridOptions });

  // 设置表格选中状态的函数
  const setTableSelection = () => {
    if (!gridApi?.grid) return;

    try {
      // 获取当前表格中的所有数据
      const tableData = gridApi.grid.getTableData().fullData;

      if (!tableData?.length) {
        // 如果数据还没加载完成，稍后重试
        setTimeout(setTableSelection, 100);
        return;
      }

      // 清除所有选中状态
      gridApi.grid.clearCheckboxRow();

      // 根据 tempSelectedApis 中的数据设置选中状态
      const tempSelectedIds = new Set(
        tempSelectedApis.value.map((api) => api.id),
      );

      tableData.forEach((row: any) => {
        if (tempSelectedIds.has(row.id)) {
          gridApi.grid.setCheckboxRow(row, true);
        }
      });
    } catch {
      // 静默处理错误，避免控制台噪音
    }
  };

  // 处理对话框中的选择变化
  const handleDialogCheckboxChange = (event: any) => {
    const currentPageRecords = event.records || [];

    // 获取当前表格中的所有数据
    const tableData = gridApi?.grid?.getTableData().fullData || [];
    const currentPageIds = new Set(tableData.map((row: any) => row.id));

    // 移除当前页面的所有API（无论是否选中）
    const otherPagesApis = tempSelectedApis.value.filter(
      (api) => !currentPageIds.has(api.id),
    );

    // 合并其他页面的选择和当前页面的选择
    tempSelectedApis.value = [...otherPagesApis, ...currentPageRecords];
  };

  // 处理代理查询完成事件
  const handleProxyQuery = () => {
    // 数据加载完成后，设置选中状态
    setTimeout(setTableSelection, 50);
  };

  // 确认API选择
  const confirmApiSelection = () => {
    selectedApis.value = [...tempSelectedApis.value];
  };

  // 设置已选择的API数据（用于初始化）
  const setSelectedApisData = (apis: ApiEndpoint[]) => {
    selectedApis.value = [...apis];
  };

  // 创建默认参数转换器
  const createDefaultTransformer = (): ParamTransformer => ({
    aiParam: '',
    apiParam: '',
    transformRule: 'none',
    description: '',
  });

  // 监听选中的API变化，自动生成工具配置
  watch(selectedApis, generateToolConfigs, { immediate: true, deep: true });

  // 监听工具配置变化，初始化参数转换器
  watch(
    toolConfigs,
    (newConfigs: ToolConfig[]) => {
      const newTransformers: Record<string, ParamTransformer[]> = {};

      newConfigs.forEach((tool: ToolConfig) => {
        // 保留已有配置或创建默认配置
        newTransformers[tool.apiId] = paramTransformers.value[tool.apiId] || [
          createDefaultTransformer(),
        ];
      });

      paramTransformers.value = newTransformers;
    },
    { immediate: true },
  );

  return {
    // 状态数据
    selectedApis,
    tempSelectedApis,
    toolConfigs,
    paramTransformers,
    testQuery,
    testLoading,
    testResults,
    finishForm,
    canProceedToNext,

    // 核心功能
    runTest,
    saveToolConfiguration,
    generateToolConfigs,

    // 参数转换器管理
    addParameterTransformer,
    removeParameterTransformer,

    // 表格组件
    Grid,
    gridApi,

    // API 选择管理
    setTableSelection,
    handleDialogCheckboxChange,
    handleProxyQuery,
    confirmApiSelection,
    setSelectedApisData,
  };
}
