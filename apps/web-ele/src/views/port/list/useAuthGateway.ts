import { onMounted, ref } from 'vue';

import { getGatewayOptions } from '#/api/common';

const useAuthGateway = () => {
  const gatewayAuthInfoMap = ref<any>({
    authGateway: {},
    authMode: {},
    rbacValidation: {},
  });
  onMounted(() => {
    const temp: any = { authGateway: {}, authMode: {}, rbacValidation: {} };
    const fn = (arr: []) => {
      // eslint-disable-next-line unicorn/no-array-reduce
      return arr.reduce((acc: any, cur: any) => {
        acc[cur.code] = cur.desc;
        return acc;
      }, {});
    };
    getGatewayOptions().then((res) => {
      res.forEach((i) => {
        temp.authGateway[i.code] = i.desc;
        const authModeObj = fn(i.authModes);

        i.authModes.forEach(({ rbacValidations }: any) => {
          const obj = fn(rbacValidations);
          Object.keys(obj).forEach((k) => {
            !temp.rbacValidation[k] && (temp.rbacValidation[k] = obj[k]);
          });
        });

        Object.keys(authModeObj).forEach((key) => {
          !temp.authMode[key] && (temp.authMode[key] = authModeObj[key]);
        });
      });

      gatewayAuthInfoMap.value = temp;
    });
  });

  return gatewayAuthInfoMap;
};

export default useAuthGateway;
