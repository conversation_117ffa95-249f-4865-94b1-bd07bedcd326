<script lang="ts" setup>
import type { VbenFormProps } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';

import { h, reactive, ref } from 'vue';

import { useVbenForm, useVbenModal } from '@vben/common-ui';
import { useAppConfig } from '@vben/hooks';

import { ElMessage, ElMessageBox } from 'element-plus';
import { z } from 'zod';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { getByEnumName, getSelectOptions } from '#/api/common';
import { requestClient } from '#/api/request';
import { getApplicationNameOptions } from '#/views/apps/common';

import GateWayCfg from './gateway-cfg.vue';
import Import from './import.vue';
import useAuthGateway from './useAuthGateway';

const { apiURL } = useAppConfig(import.meta.env, false);
const selectList = ref([]);

let exportParams: any = {};

async function fetchPublicData(payload: any, tableApi: any) {
  const res = await requestClient.get(
    '/hera/safe-center/b/safeInterface/page',
    { params: payload },
  );
  selectList.value = [];
  tableApi.formApi.getValues().then((res: any) => {
    // 储藏查询成功的参数,导出时候使用
    exportParams = res;
  });
  return {
    total: res.totalCount,
    items: res.data,
  }; // 返回响应数据
}
// 编辑
async function upBase(data: object) {
  return requestClient.post('/hera/safe-center/b/safeInterface/upBase', data, {
    responseReturn: 'body',
  });
}
interface TupStatus {
  ids?: string;
  up: boolean;
}
// 上下线
async function upStatus(data: string | TupStatus, isBatch: boolean) {
  return isBatch
    ? requestClient.get(`/hera/safe-center/b/safeInterface/upStatus${data}`, {
        responseReturn: 'body',
      })
    : requestClient.get('/hera/safe-center/b/safeInterface/upStatus', {
        params: data,
        responseReturn: 'body',
      });
}
const service = {
  fetchPublicData,
  getByEnumName,
  upBase,
  upStatus,
};

const formOptions: VbenFormProps = {
  // 默认展开
  collapsed: false,
  showCollapseButton: false,
  schema: [
    {
      component: 'ApiSelect',
      componentProps: {
        placeholder: '请输入',
        clearable: true,
        filterable: true,
        api: getApplicationNameOptions,
      },
      fieldName: 'applicationName',
      label: '服务名',
    },
    {
      component: 'ApiSelect',
      componentProps: {
        placeholder: '请输入',
        clearable: true,
        filterable: true,
        api: getSelectOptions('interfacestatus'),
      },
      fieldName: 'status',
      label: '状态',
    },
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入',
      },
      fieldName: 'apiClass',
      label: 'API类',
    },
    {
      component: 'Input',
      componentProps: {
        placeholder: '请输入API路径',
        clearable: true,
      },
      fieldName: 'apiPath',
      label: 'API路径',
    },
  ],
  submitButtonOptions: {
    content: '查询',
  },
  // 是否在字段值改变时提交表单
  submitOnChange: false,
  // 按下回车时是否提交表单
  submitOnEnter: false,
};

const gridOptions: VxeGridProps<any> = {
  minHeight: 350,
  checkboxConfig: {
    highlight: true,
    labelField: 'name',
    checkMethod: ({ row }) => {
      return row?.authInfoList?.length;
    },
  },
  showOverflow: false,

  columns: [
    { align: 'left', title: '', type: 'checkbox', width: 50 },
    { field: 'applicationName', title: '服务名', minWidth: 100 },
    { field: 'apiClass', title: 'API类', minWidth: 100 },
    {
      align: 'left',
      field: 'apiPath',
      title: 'path路径',
      minWidth: 200,
      slots: { default: 'apiPath' },
    },
    {
      field: 'apiWay',
      title: '请求方式',
      slots: { default: 'apiWay' },
      minWidth: 90,
    },
    { field: 'apiDesc', title: '接口描述', minWidth: 50 },
    {
      align: 'left',
      field: 'authInfoList',
      title: '调用网关',
      slots: { default: 'authInfoList' },
      minWidth: 200,
    },
    { field: 'encryptionRequiredDesc', title: '加解密', minWidth: 50 },
    { field: 'statusDesc', title: '状态', minWidth: 50 },
    { field: 'updatedBy', title: '维护人', minWidth: 50 },
    {
      field: 'action',
      fixed: 'right',
      slots: { default: 'action' },
      title: '操作',
      width: 200,
    },
  ],
  keepSource: true,
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        return await service.fetchPublicData(
          {
            currentPage: page.currentPage,
            pageSize: page.pageSize,
            ...formValues,
          },
          GridApi,
        );
      },
    },
  },
  toolbarConfig: {
    // 是否显示搜索表单控制按钮
    // @ts-ignore 正式环境时有完整的类型声明
    // search: true,
  },
};

const [Grid, GridApi] = useVbenVxeGrid({ formOptions, gridOptions });

const checkboxAll = (event: any) => {
  selectList.value = event.records;
};

const checkboxChange = (event: any) => {
  selectList.value = event.records;
};
interface TrowInfo {
  applicationName: string;
  apiPathList: Array<string>;
  apiWayList: Array<string>;
  id: string;
}
const rowInfo = reactive<TrowInfo>({
  applicationName: '',
  apiPathList: [],
  apiWayList: [],
  id: '',
});

const [BaseForm, BaseFormApi] = useVbenForm({
  // 所有表单项共用，可单独在表单内覆盖
  commonConfig: {
    // 所有表单项
    componentProps: {
      class: 'w-full',
    },
  },
  layout: 'horizontal',
  schema: [
    {
      fieldName: 'authInfo',
      label: '允许调用网关',
      rules: z.array(
        z.object({
          authGateway: z.string(),
          authMode: z.string(),
        }),
      ),
      component: h(GateWayCfg, {}),
      formItemClass: 'items-start',
      labelClass: 'pt-2',
    },
    {
      component: 'ApiSelect',
      componentProps: {
        clearable: true,
        filterOption: true,
        options: [],
        placeholder: '请选择',
        showSearch: true,
        api: getSelectOptions('encryptionrequired'),
      },
      fieldName: 'encryptionRequired',
      label: '统一加解密',
    },
    {
      component: 'Input',
      componentProps: {
        allowClear: true,
        showWordlimit: true,
        maxlength: '500',
        type: 'textarea',
        placeholder: '请输入不超过500字',
        showSearch: true,
      },
      fieldName: 'apiDesc',
      label: '接口描述',
    },
  ],
  wrapperClass: 'grid-cols-1',
  showDefaultActions: false,
});
// 提交表单
const onSubmit = () => {
  BaseFormApi.validateAndSubmitForm().then((formData) => {
    if (formData) {
      const params = {
        id: rowInfo.id,
        ...formData,
      };
      service.upBase(params).then((res) => {
        if (res.code === '10000') {
          ElMessage.success(res.msg);
          modalApi.close();
          GridApi.reload();
        } else {
          ElMessage.error(res.msg);
        }
      });
    }
  });
};
const [Modal, modalApi] = useVbenModal({
  confirmText: '提交',
  onConfirm: () => {
    BaseFormApi.submitForm();
  },
  onCancel: () => {
    BaseFormApi.resetForm();
    modalApi.close();
    rowInfo.applicationName = '';
    rowInfo.apiPathList = [];
    rowInfo.apiWayList = [];
  },
});

const openModal = (row: any) => {
  rowInfo.applicationName = row.applicationName || '';
  rowInfo.apiPathList = row.apiPathList || [];
  rowInfo.apiWayList = row.apiWayList || [];
  rowInfo.id = row.id || '';
  modalApi.open();
  const authInfo = row.authInfoList;
  BaseFormApi.setValues({ ...row, authInfo });
};

const toMessage = (res: any) => {
  if (res.code === '10000') {
    ElMessage.success(res.msg);
  } else {
    ElMessage.error(res.msg);
  }
};

const apiMessageBox = (api: () => Promise<any>, text: string) => {
  ElMessageBox.confirm(`确认${text}吗?`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(() => {
      api()
        .then((res) => {
          toMessage(res);
          GridApi.reload();
        })
        .catch((error) => {
          toMessage(error);
        });
    })
    .catch(() => {
      ElMessage({
        type: 'info',
        message: '已取消',
      });
    });
};
// 上下线操作
const interfaceUplinkDownlink = (type: string, row: any) => {
  if (!row?.authInfoList?.length) {
    return ElMessage.warning('请先完善调用网关配置');
  }
  switch (type) {
    case 'down': {
      apiMessageBox(async () => {
        const res = await service.upStatus({ ids: row.id, up: false }, false);
        return res;
      }, '下线');
      break;
    }
    case 'up': {
      apiMessageBox(async () => {
        const res = await service.upStatus({ ids: row.id, up: true }, false);
        return res;
      }, '发布');
      break;
    }
    default: {
      break;
    }
  }
};
// 批量上下线
const batch = (text: string, isUp: boolean) => {
  if (selectList.value.length === 0) {
    ElMessage.warning('请选择发布接口');
    return;
  }
  const arr = selectList.value.map((item: any) => {
    return item.id;
  });
  const queryParams = arr.map((item) => `ids=${item}`).join('&');
  const queryString = `?${queryParams}&up=${isUp}`;
  apiMessageBox(async () => {
    const res = await service.upStatus(queryString, true);
    return res;
  }, text);
};
// 导出
const out = () => {
  // 创建一个隐藏的 <a> 元素
  const link = document.createElement('a');
  link.style.display = 'none';
  // 查询参数对象
  const queryParams = {
    applicationName: exportParams.applicationName || '',
    status: exportParams.status || '',
    apiClass: exportParams.apiClass || '',
    apiPath: exportParams.apiPath || '',
  };
  // 将查询参数对象转换为查询字符串
  const queryString = new URLSearchParams(queryParams).toString();

  // 设置下载文件的 URL 和查询参数
  link.href = `${apiURL}/hera/safe-center/b/safeInterface/export?${queryString}`;
  // link.download = '文件名';
  // 将 <a> 元素添加到页面中
  document.body.append(link);
  // 触发点击事件，开始下载
  link.click();
  // 清理操作
  link.remove();
};

const importRef = ref<any>(null);
const toImport = () => {
  if (importRef.value) {
    importRef.value.open();
  }
};

const gatewayAuthInfoMap = useAuthGateway();
</script>

<template>
  <div>
    <ElCard class="port-list vp-raw w-full">
      <Grid
        :grid-events="{
          checkboxAll,
          checkboxChange,
        }"
      >
        <template #toolbar>
          <div class="mb-2">
            <ElButton type="primary" @click="toImport"> 导入 </ElButton>
            <ElButton type="primary" @click="out" class="mr-2"> 导出 </ElButton>
            <ElDropdown>
              <ElButton type="primary" class="mr-2"> 批量上下线 </ElButton>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item @click="batch('批量上线', true)">
                    批量上线
                  </el-dropdown-item>
                  <el-dropdown-item @click="batch('批量下线', false)">
                    批量下线
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </ElDropdown>
          </div>
        </template>
        <template #action="{ row }">
          <ElButton type="primary" size="small" @click="openModal(row)">
            编辑
          </ElButton>
          <ElButton
            type="success"
            size="small"
            @click="interfaceUplinkDownlink('up', row)"
            v-if="row.status === 'OFFLINE'"
          >
            发布
          </ElButton>
          <ElButton
            type="danger"
            size="small"
            @click="interfaceUplinkDownlink('down', row)"
            v-if="row.status === 'ONLINE' || !row.status"
          >
            下线
          </ElButton>
        </template>
        <template #apiPath="{ row }">
          {{ row.apiPathList ? row.apiPathList.join('，') : '' }}
        </template>
        <template #apiWay="{ row }">
          {{ row.apiPathList ? row.apiWayList.join('，') : '' }}
        </template>
        <template #authInfoList="{ row }">
          <div
            v-for="(item, idx) in row.authInfoList"
            :key="idx"
            class="mb-[6px]"
          >
            <span>{{ gatewayAuthInfoMap.authGateway[item.authGateway] }}</span>
            &nbsp;|&nbsp;
            <span>{{ gatewayAuthInfoMap.authMode[item.authMode] }}</span>

            <span v-if="item.rbacValidation">
              &nbsp;|&nbsp;
              {{ gatewayAuthInfoMap.rbacValidation[item.rbacValidation] }}
            </span>
          </div>
        </template>
      </Grid>
    </ElCard>
    <Modal class="w-[800px]" title="编辑接口">
      <div
        style="
          padding: 12px;
          margin: 0 0 5px 10px;
          border: 1px dashed #e4e4e7;
          border-radius: 10px;
        "
      >
        <p
          style="
            margin-bottom: 10px;
            word-break: break-all;
            word-wrap: break-word;
          "
        >
          <span style="display: inline-block; width: 80px; font-weight: bold">
            服务名:
          </span>
          {{ rowInfo?.applicationName }}
        </p>
        <p
          class="fixed-width w-200 mb-3"
          style="word-break: break-all; word-wrap: break-word"
        >
          <span style="display: inline-block; width: 80px; font-weight: bold">
            path路径:
          </span>
          {{ rowInfo.apiPathList ? rowInfo.apiPathList.join('，') : '' }}
        </p>
        <p
          class="fixed-width mb-3"
          style="word-break: break-all; word-wrap: break-word"
        >
          <span style="display: inline-block; width: 80px; font-weight: bold">
            请求方式:
          </span>
          {{ rowInfo?.apiWayList ? rowInfo?.apiWayList.join('，') : '' }}
        </p>
      </div>
      <BaseForm />
      <template #footer>
        <div class="flex flex-1 items-center">
          <el-link
            target="_blank"
            :underline="false"
            href="https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=65478951"
          >
            <span
              class="icon-[mdi--alert-circle] text-muted-foreground mr-1 size-4"
            ></span>
            鉴权错误码说明
          </el-link>
          <div class="flex flex-1 justify-end gap-2">
            <ElButton @click="modalApi.close()">取消</ElButton>
            <ElButton type="primary" @click="onSubmit()">提交</ElButton>
          </div>
        </div>
      </template>
    </Modal>
    <Import ref="importRef" @refresh="GridApi.reload()" />
  </div>
</template>
