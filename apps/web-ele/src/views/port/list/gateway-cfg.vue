<script setup lang="ts">
import { computed, onMounted, ref, watch } from 'vue';

import { getGatewayOptions } from '#/api/common';

interface GatewayCfg {
  authMode: string;
  authGateway: string;
  rbacValidation?: string;
}

const props = defineProps<{
  modelValue?: Array<GatewayCfg>;
}>();

const emit = defineEmits(['update:modelValue']);

const innerValue = ref<Array<GatewayCfg>>(
  props.modelValue?.length ? props.modelValue : [{}],
);

const mockData = ref([]);
const rbacValidationMap = ref({});

const isExternalUpdate = ref(false);

onMounted(() => {
  getGatewayOptions().then((res) => {
    mockData.value = res;
  });
});

const options = computed(() =>
  mockData.value.map(({ code, desc, authModes }) => {
    authModes.forEach(({ rbacValidations, code: authModeCode }) => {
      if (!rbacValidationMap.value[authModeCode]) {
        rbacValidationMap.value[authModeCode] = rbacValidations;
      }
    });
    return {
      label: desc,
      value: code,
    };
  }),
);

const findOptions = (authGateway: string, type: string) => {
  if (!authGateway || !type || !mockData.value?.length) return [];
  const currentOption = mockData.value.find((opt) => opt.code === authGateway);
  return currentOption?.[type] || [];
};

// 处理第一个选择器的变更
const handleFirstChange = (val: number | string, idx: number) => {
  // 清空当前行的鉴权模式和RBAC权限
  innerValue.value[idx].authMode = undefined;
  innerValue.value[idx].rbacValidation = undefined;
};

const addRow = () => {
  innerValue.value.push({});
};

const deleteRow = (index: number) => {
  innerValue.value.splice(index, 1);
};

watch(innerValue.value, (val) => {
  if (isExternalUpdate.value) return;
  emit('update:modelValue', val);
});

watch(
  () => props.modelValue,
  (val) => {
    isExternalUpdate.value = true;
    innerValue.value = val;
    isExternalUpdate.value = false;
  },
);
</script>

<template>
  <div>
    <!-- <div class="cascade-select pr-[58px]">
      <span class="select-item-wrapper"> 网关 </span>
      <span class="select-item-wrapper"> 鉴权模式 </span>
      <span class="select-item-wrapper"> RBAC权限 </span>
    </div> -->
    <div class="cascade-select" v-for="(_it, index) in innerValue" :key="index">
      <span class="select-item-wrapper">
        <span class="cascade-select-item">
          <el-select
            clearable
            v-model="innerValue[index].authGateway"
            placeholder="请选择网关"
            @change="(val) => handleFirstChange(val, index)"
          >
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </span>

        <span class="cascade-select-item">
          <el-select
            clearable
            v-model="innerValue[index].authMode"
            :placeholder="
              innerValue[index].authGateway ? '请选择鉴权模式' : '请先选择网关'
            "
            :disabled="!innerValue[index].authGateway"
          >
            <el-option
              v-for="item in findOptions(
                innerValue[index].authGateway,
                'authModes',
              )"
              :key="item.code"
              :label="item.desc"
              :value="item.code"
            />
          </el-select>
        </span>

        <span class="cascade-select-item">
          <el-select
            clearable
            v-model="innerValue[index].rbacValidation"
            placeholder="请选择RBAC权限"
            :disabled="!innerValue[index].authMode"
            v-if="rbacValidationMap[innerValue[index].authMode]?.length > 0"
          >
            <el-option
              v-for="it in rbacValidationMap[innerValue[index].authMode]"
              :key="it.code"
              :label="it.desc"
              :value="it.code"
            />
          </el-select>
        </span>
      </span>
      <span class="add-row-btn">
        <el-link
          :underline="false"
          type="danger"
          v-if="index !== 0"
          @click="deleteRow(index)"
        >
          删除
        </el-link>
      </span>
    </div>
    <span>
      <el-button type="default" @click="addRow">添加网关</el-button>
    </span>
  </div>
</template>

<style lang="scss" scoped>
.cascade-select {
  display: flex;
  margin-bottom: 10px;

  &-item {
    flex: 1;
  }
}

.select-item-wrapper {
  display: flex;
  flex: 1;
  gap: 6px;
}

.add-row-btn {
  display: inline-block;
  min-width: 60px;
  text-align: right;
}
</style>
