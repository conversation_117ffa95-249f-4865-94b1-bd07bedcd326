<script lang="ts" setup>
import { defineEmits, ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';
import { useAppConfig } from '@vben/hooks';

import { ElButton, ElMessage, ElUpload } from 'element-plus';

// 定义事件和参数类型
const emit = defineEmits(['refresh']);

const { apiURL } = useAppConfig(import.meta.env, false);

async function exportTemp() {
  // 创建一个隐藏的 <a> 元素
  const link = document.createElement('a');
  link.style.display = 'none';
  // 设置下载文件的 URL 和文件名
  link.href = `${apiURL}/hera/safe-center/b/safeInterface/exportTemp`;
  // link.download = '文件名';
  // 将 <a> 元素添加到页面中
  document.body.append(link);
  // 触发点击事件，开始下载
  link.click();
  // 清理操作
  link.remove();
}

// 使用 useVbenModal 获取模态框和 API
const [Modal, modalApi] = useVbenModal({
  showConfirmButton: false,
  cancelText: '关闭',
});

// 打开模态框的方法
const open = () => {
  modalApi.open();
};

const fileList = ref([]);

// 限制上传文件格式为 Excel
const beforeUpload = (file: any) => {
  const allowedTypes = [
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  ];
  const isValid = allowedTypes.includes(file.type);

  if (!isValid) {
    ElMessage.error('只能上传 Excel 文件');
  }

  return isValid;
};

const uploadSuccess = (res: any) => {
  if (res.code === '10000') {
    ElMessage.success(res.msg);
    emit('refresh');
  } else {
    ElMessage.error(res.msg);
  }
  fileList.value = [];
};

const uploadError = (res: any) => {
  ElMessage.error(res.msg);
  fileList.value = [];
};

// 定义暴露的方法
defineExpose({
  open,
});
</script>

<template>
  <div>
    <!-- 模态框 -->
    <Modal class="w-[600px]" title="接口导入">
      <!-- 文件上传组件 -->
      <div class="describe">
        <div class="upload-title">导入操作指南</div>
        <div style="display: flex; align-items: center">
          点击
          <ElButton
            type="primary"
            size="default"
            class="donwload-tele"
            link
            @click="exportTemp"
          >
            下载模板
          </ElButton>
          按照表格模板填好对应信息
        </div>
      </div>
      <ElUpload
        v-model:file-list="fileList"
        class="upload-demo"
        drag
        :with-credentials="true"
        :action="`${apiURL}/hera/safe-center/b/safeInterface/import`"
        accept=".xls,.xlsx"
        :limit="1"
        :before-upload="beforeUpload"
        :on-success="uploadSuccess"
        :on-error="uploadError"
      >
        <!-- <CloudUpload class="size-10" /> -->
        <span
          class="icon-[mdi--cloud-upload] text-muted-foreground size-10"
        ></span>
        <div class="el-upload__text">
          <em>点击上传文件或将文件拖拽到这里进行上传</em>
        </div>
        <template #tip>
          <div class="el-upload__tip">只能上传 Excel 文件（.xls 或 .xlsx）</div>
        </template>
      </ElUpload>
    </Modal>
  </div>
</template>
<style>
.describe {
  height: 50px;
  margin-bottom: 10px;
  font-size: 15px;
}
</style>
