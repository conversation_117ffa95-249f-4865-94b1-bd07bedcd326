/*
 * @Description: Mock API数据 - 用于测试API选择功能
 */

import type {
  ApiEndpoint,
  PageQueryParams,
  PageQueryResponse,
} from '../../views/mcp/tool/tool-add/types';

// Mock API数据生成器
const generateMockApiData = (): ApiEndpoint[] => {
  const methods = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'];
  const services = [
    '用户管理服务',
    '订单管理服务',
    '商品管理服务',
    '支付管理服务',
    '库存管理服务',
    '物流管理服务',
    '营销管理服务',
    '客服管理服务',
    '数据分析服务',
    '系统管理服务',
  ];

  const apiTemplates = [
    // 用户管理相关
    {
      service: '用户管理服务',
      apis: [
        {
          method: 'GET',
          path: '/api/v1/users',
          name: '获取用户列表',
          desc: '分页查询用户信息列表',
        },
        {
          method: 'GET',
          path: '/api/v1/users/{userId}',
          name: '获取用户详情',
          desc: '根据用户ID获取用户详细信息',
        },
        {
          method: 'POST',
          path: '/api/v1/users',
          name: '创建用户',
          desc: '创建新用户账户',
        },
        {
          method: 'PUT',
          path: '/api/v1/users/{userId}',
          name: '更新用户信息',
          desc: '更新指定用户的信息',
        },
        {
          method: 'DELETE',
          path: '/api/v1/users/{userId}',
          name: '删除用户',
          desc: '删除指定用户账户',
        },
        {
          method: 'POST',
          path: '/api/v1/users/{userId}/reset-password',
          name: '重置用户密码',
          desc: '重置指定用户的登录密码',
        },
        {
          method: 'GET',
          path: '/api/v1/users/{userId}/profile',
          name: '获取用户档案',
          desc: '获取用户的详细档案信息',
        },
        {
          method: 'PUT',
          path: '/api/v1/users/{userId}/status',
          name: '更新用户状态',
          desc: '启用或禁用用户账户',
        },
      ],
    },

    // 订单管理相关
    {
      service: '订单管理服务',
      apis: [
        {
          method: 'GET',
          path: '/api/v1/orders',
          name: '获取订单列表',
          desc: '分页查询订单信息',
        },
        {
          method: 'GET',
          path: '/api/v1/orders/{orderId}',
          name: '获取订单详情',
          desc: '根据订单ID获取订单详细信息',
        },
        {
          method: 'POST',
          path: '/api/v1/orders',
          name: '创建订单',
          desc: '创建新的订单',
        },
        {
          method: 'PUT',
          path: '/api/v1/orders/{orderId}/status',
          name: '更新订单状态',
          desc: '更新订单的处理状态',
        },
        {
          method: 'DELETE',
          path: '/api/v1/orders/{orderId}',
          name: '取消订单',
          desc: '取消指定的订单',
        },
        {
          method: 'GET',
          path: '/api/v1/orders/{orderId}/items',
          name: '获取订单商品',
          desc: '获取订单中的商品列表',
        },
        {
          method: 'POST',
          path: '/api/v1/orders/{orderId}/refund',
          name: '申请退款',
          desc: '为订单申请退款',
        },
        {
          method: 'GET',
          path: '/api/v1/orders/statistics',
          name: '订单统计',
          desc: '获取订单相关统计数据',
        },
      ],
    },

    // 商品管理相关
    {
      service: '商品管理服务',
      apis: [
        {
          method: 'GET',
          path: '/api/v1/products',
          name: '获取商品列表',
          desc: '分页查询商品信息',
        },
        {
          method: 'GET',
          path: '/api/v1/products/{productId}',
          name: '获取商品详情',
          desc: '根据商品ID获取详细信息',
        },
        {
          method: 'POST',
          path: '/api/v1/products',
          name: '创建商品',
          desc: '添加新的商品',
        },
        {
          method: 'PUT',
          path: '/api/v1/products/{productId}',
          name: '更新商品信息',
          desc: '更新商品的基本信息',
        },
        {
          method: 'DELETE',
          path: '/api/v1/products/{productId}',
          name: '删除商品',
          desc: '删除指定商品',
        },
        {
          method: 'GET',
          path: '/api/v1/products/search',
          name: '搜索商品',
          desc: '根据关键词搜索商品',
        },
        {
          method: 'PUT',
          path: '/api/v1/products/{productId}/price',
          name: '更新商品价格',
          desc: '更新商品的销售价格',
        },
        {
          method: 'GET',
          path: '/api/v1/products/categories',
          name: '获取商品分类',
          desc: '获取商品分类列表',
        },
      ],
    },

    // 支付管理相关
    {
      service: '支付管理服务',
      apis: [
        {
          method: 'POST',
          path: '/api/v1/payments',
          name: '创建支付',
          desc: '创建新的支付订单',
        },
        {
          method: 'GET',
          path: '/api/v1/payments/{paymentId}',
          name: '获取支付详情',
          desc: '查询支付订单详情',
        },
        {
          method: 'POST',
          path: '/api/v1/payments/{paymentId}/confirm',
          name: '确认支付',
          desc: '确认支付结果',
        },
        {
          method: 'POST',
          path: '/api/v1/payments/{paymentId}/refund',
          name: '申请退款',
          desc: '申请支付退款',
        },
        {
          method: 'GET',
          path: '/api/v1/payments/methods',
          name: '获取支付方式',
          desc: '获取可用的支付方式列表',
        },
        {
          method: 'GET',
          path: '/api/v1/payments/history',
          name: '支付历史',
          desc: '查询支付历史记录',
        },
      ],
    },

    // 库存管理相关
    {
      service: '库存管理服务',
      apis: [
        {
          method: 'GET',
          path: '/api/v1/inventory/{productId}',
          name: '查询库存',
          desc: '查询商品库存数量',
        },
        {
          method: 'PUT',
          path: '/api/v1/inventory/{productId}',
          name: '更新库存',
          desc: '更新商品库存数量',
        },
        {
          method: 'POST',
          path: '/api/v1/inventory/inbound',
          name: '入库操作',
          desc: '商品入库操作',
        },
        {
          method: 'POST',
          path: '/api/v1/inventory/outbound',
          name: '出库操作',
          desc: '商品出库操作',
        },
        {
          method: 'GET',
          path: '/api/v1/inventory/alerts',
          name: '库存预警',
          desc: '获取库存预警信息',
        },
        {
          method: 'GET',
          path: '/api/v1/inventory/reports',
          name: '库存报表',
          desc: '生成库存统计报表',
        },
      ],
    },

    // 物流管理相关
    {
      service: '物流管理服务',
      apis: [
        {
          method: 'POST',
          path: '/api/v1/logistics/shipments',
          name: '创建发货单',
          desc: '创建物流发货单',
        },
        {
          method: 'GET',
          path: '/api/v1/logistics/shipments/{shipmentId}',
          name: '查询物流信息',
          desc: '查询发货单物流状态',
        },
        {
          method: 'PUT',
          path: '/api/v1/logistics/shipments/{shipmentId}/status',
          name: '更新物流状态',
          desc: '更新发货单状态',
        },
        {
          method: 'GET',
          path: '/api/v1/logistics/tracking/{trackingNumber}',
          name: '物流跟踪',
          desc: '根据运单号跟踪物流',
        },
        {
          method: 'GET',
          path: '/api/v1/logistics/providers',
          name: '物流服务商',
          desc: '获取物流服务商列表',
        },
      ],
    },

    // 营销管理相关
    {
      service: '营销管理服务',
      apis: [
        {
          method: 'GET',
          path: '/api/v1/promotions',
          name: '获取促销活动',
          desc: '获取促销活动列表',
        },
        {
          method: 'POST',
          path: '/api/v1/promotions',
          name: '创建促销活动',
          desc: '创建新的促销活动',
        },
        {
          method: 'PUT',
          path: '/api/v1/promotions/{promotionId}',
          name: '更新促销活动',
          desc: '更新促销活动信息',
        },
        {
          method: 'GET',
          path: '/api/v1/coupons',
          name: '获取优惠券',
          desc: '获取优惠券列表',
        },
        {
          method: 'POST',
          path: '/api/v1/coupons',
          name: '创建优惠券',
          desc: '创建新的优惠券',
        },
        {
          method: 'POST',
          path: '/api/v1/coupons/{couponId}/use',
          name: '使用优惠券',
          desc: '使用指定优惠券',
        },
      ],
    },

    // 客服管理相关
    {
      service: '客服管理服务',
      apis: [
        {
          method: 'GET',
          path: '/api/v1/tickets',
          name: '获取工单列表',
          desc: '获取客服工单列表',
        },
        {
          method: 'POST',
          path: '/api/v1/tickets',
          name: '创建工单',
          desc: '创建新的客服工单',
        },
        {
          method: 'PUT',
          path: '/api/v1/tickets/{ticketId}',
          name: '更新工单',
          desc: '更新工单信息',
        },
        {
          method: 'POST',
          path: '/api/v1/tickets/{ticketId}/reply',
          name: '回复工单',
          desc: '回复客服工单',
        },
        {
          method: 'GET',
          path: '/api/v1/feedback',
          name: '获取反馈',
          desc: '获取用户反馈列表',
        },
        {
          method: 'POST',
          path: '/api/v1/feedback',
          name: '提交反馈',
          desc: '提交用户反馈',
        },
      ],
    },

    // 数据分析相关
    {
      service: '数据分析服务',
      apis: [
        {
          method: 'GET',
          path: '/api/v1/analytics/sales',
          name: '销售数据分析',
          desc: '获取销售数据统计',
        },
        {
          method: 'GET',
          path: '/api/v1/analytics/users',
          name: '用户数据分析',
          desc: '获取用户行为数据',
        },
        {
          method: 'GET',
          path: '/api/v1/analytics/products',
          name: '商品数据分析',
          desc: '获取商品销售数据',
        },
        {
          method: 'GET',
          path: '/api/v1/analytics/reports',
          name: '生成报表',
          desc: '生成各类数据报表',
        },
        {
          method: 'GET',
          path: '/api/v1/analytics/dashboard',
          name: '数据看板',
          desc: '获取数据看板信息',
        },
      ],
    },

    // 系统管理相关
    {
      service: '系统管理服务',
      apis: [
        {
          method: 'GET',
          path: '/api/v1/system/config',
          name: '获取系统配置',
          desc: '获取系统配置信息',
        },
        {
          method: 'PUT',
          path: '/api/v1/system/config',
          name: '更新系统配置',
          desc: '更新系统配置',
        },
        {
          method: 'GET',
          path: '/api/v1/system/logs',
          name: '获取系统日志',
          desc: '获取系统操作日志',
        },
        {
          method: 'GET',
          path: '/api/v1/system/health',
          name: '系统健康检查',
          desc: '检查系统运行状态',
        },
        {
          method: 'POST',
          path: '/api/v1/system/backup',
          name: '数据备份',
          desc: '执行数据备份操作',
        },
        {
          method: 'POST',
          path: '/api/v1/system/restore',
          name: '数据恢复',
          desc: '执行数据恢复操作',
        },
      ],
    },
  ];

  const mockData: ApiEndpoint[] = [];
  let idCounter = 1;

  apiTemplates.forEach((serviceGroup) => {
    serviceGroup.apis.forEach((api) => {
      mockData.push({
        id: `api_${idCounter.toString().padStart(4, '0')}`,
        apiMethod: api.method,
        apiUrl: api.path,
        apiName: api.name,
        apiService: serviceGroup.service,
        description: api.desc,
      });
      idCounter++;
    });
  });

  return mockData;
};

// 生成mock数据
const MOCK_API_DATA = generateMockApiData();

// Mock分页查询API函数
export const mockPageQueryApiApi = async (
  params: PageQueryParams,
): Promise<PageQueryResponse<ApiEndpoint>> => {
  const { currentPage, pageSize, searchKey = '' } = params;

  // 模拟网络延迟
  await new Promise((resolve) =>
    setTimeout(resolve, 300 + Math.random() * 500),
  );

  // 过滤数据（搜索功能）
  let filteredData = MOCK_API_DATA;
  if (searchKey.trim()) {
    const keyword = searchKey.toLowerCase();
    filteredData = MOCK_API_DATA.filter(
      (item) =>
        item.apiName.toLowerCase().includes(keyword) ||
        item.apiUrl.toLowerCase().includes(keyword) ||
        item.apiService.toLowerCase().includes(keyword) ||
        item.apiMethod.toLowerCase().includes(keyword),
    );
  }

  // 分页处理
  const startIndex = (currentPage - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  const pageData = filteredData.slice(startIndex, endIndex);

  return {
    data: pageData,
    total: filteredData.length,
    totalCount: filteredData.length,
  };
};

// 导出mock数据（用于其他地方可能需要）
export { MOCK_API_DATA };
