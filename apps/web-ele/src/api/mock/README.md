# Mock API 数据说明

## 概述

为了方便测试API选择功能，我们创建了一套完整的Mock API数据。这些数据模拟了真实的业务场景，包含了多个服务模块的API接口。

## 文件结构

```
src/api/mock/
├── apiData.ts          # Mock API数据和分页查询函数
└── README.md          # 说明文档（本文件）
```

## Mock数据特性

### 📊 数据规模
- **总计**: 约80+个API接口
- **服务模块**: 10个不同的业务服务
- **请求方法**: GET、POST、PUT、DELETE、PATCH
- **支持功能**: 分页、搜索、过滤

### 🏢 业务服务模块

1. **用户管理服务** - 8个API
   - 用户CRUD操作
   - 密码重置、状态管理等

2. **订单管理服务** - 8个API
   - 订单生命周期管理
   - 退款、统计等功能

3. **商品管理服务** - 8个API
   - 商品信息管理
   - 分类、搜索、价格管理

4. **支付管理服务** - 6个API
   - 支付流程管理
   - 退款、支付方式等

5. **库存管理服务** - 6个API
   - 库存查询、更新
   - 入库出库、预警报表

6. **物流管理服务** - 5个API
   - 发货单管理
   - 物流跟踪、服务商管理

7. **营销管理服务** - 6个API
   - 促销活动管理
   - 优惠券系统

8. **客服管理服务** - 6个API
   - 工单系统
   - 用户反馈管理

9. **数据分析服务** - 5个API
   - 各类数据统计
   - 报表生成

10. **系统管理服务** - 6个API
    - 系统配置管理
    - 日志、备份恢复

## 使用方法

### 🔧 启用Mock数据

在 `src/api/mcp.ts` 文件中，找到以下配置：

```typescript
// 开发环境使用mock数据的开关
const USE_MOCK_DATA = true; // 设置为 false 使用真实API
```

- 设置为 `true`: 使用Mock数据（推荐用于开发测试）
- 设置为 `false`: 使用真实API接口

### 🔍 搜索功能

Mock数据支持以下字段的搜索：
- API名称 (`apiName`)
- API路径 (`apiUrl`) 
- 服务名称 (`apiService`)
- 请求方法 (`apiMethod`)

搜索是模糊匹配，不区分大小写。

### 📄 分页功能

- 支持自定义页面大小
- 支持页码跳转
- 返回总数据量信息

## 测试场景

### 基础功能测试
1. **分页测试**: 设置不同的页面大小（如10、20、50）
2. **搜索测试**: 
   - 搜索 "用户" - 应该返回用户管理相关的API
   - 搜索 "GET" - 应该返回所有GET方法的API
   - 搜索 "/api/v1/orders" - 应该返回订单相关的API

### 交互功能测试
1. **选择测试**: 
   - 单选API
   - 批量选择API
   - 跨页选择API

2. **回显测试**:
   - 选择一些API后关闭对话框
   - 重新打开对话框，验证之前选择的API是否正确回显

3. **删除测试**:
   - 在已选择列表中删除API
   - 验证删除后的状态同步

## 数据示例

```typescript
{
  id: "api_0001",
  apiMethod: "GET",
  apiUrl: "/api/v1/users",
  apiName: "获取用户列表",
  apiService: "用户管理服务",
  description: "分页查询用户信息列表"
}
```

## 性能特性

- **模拟延迟**: 300-800ms随机延迟，模拟真实网络环境
- **内存效率**: 数据在内存中生成，响应快速
- **搜索优化**: 客户端过滤，支持实时搜索

## 切换到真实API

当后端API准备就绪时，只需要：

1. 将 `USE_MOCK_DATA` 设置为 `false`
2. 确保后端API返回的数据格式与Mock数据一致
3. 测试真实API的分页和搜索功能

## 扩展Mock数据

如需添加更多Mock数据，可以在 `apiData.ts` 中的 `apiTemplates` 数组中添加新的服务模块和API定义。

---

**注意**: Mock数据仅用于开发和测试，生产环境请确保使用真实的API接口。
