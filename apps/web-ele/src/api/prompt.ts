/*
 * @Author: <PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-07-17 17:06:00
 * @LastEditors: zhangqian <EMAIL>
 * @LastEditTime: 2025-07-28 15:54:16
 * @Description: Prompt模板管理相关API接口
 * @FilePath: /dev-platform-ui/apps/web-ele/src/api/prompt.ts
 */
import { requestClient } from '#/api/request';

import { merApiURL } from './core/auth';
// Prompt模板接口类型定义
export interface PromptTemplate {
  promptTemplateNo?: string;
  promptTemplateName?: string;
  promptTemplateDesc?: string;
  createEmpName?: string;
  createEmpCode?: string;
  createEmpDepartmentName?: string;
  createEmpDepartmentCode?: string;
  updatedTime?: string; // 更新时间
  variable?: string; // 后端接口返回的是字符串，需要解析为数组
  variables: string[]; // 解析后的变量数组
}

export interface PromptVersion {
  createEmpCode?: string;
  createEmpName?: string;
  createEmpDepartmentCode?: number;
  createEmpDepartmentName?: string;
  createdTime?: string;
  dataVersion?: number;
  promptTemplateNo?: string;
  systemPrompt?: string;
  userPrompt?: string;
  variable?: string; // 后端接口返回的是字符串，需要解析为数组
  variables?: string[]; // 前端解析的数组
  isCurrent?: boolean;
}

export interface PromptStatistics {
  allCallTimes?: number; // 总调用次数
  allConsumeToken?: number; // 总消耗Token
  avgResponseDuration?: number; // 平均响应时长
  promptTemplateNo?: number; // 模板编号
  todayCallTimes?: number; // 今日调用次数
}

export interface PromptCallRecord {
  callRecordNo?: string;
  callStatus?: string; // 调用状态，SUCCESS-成功，FAILED-失败
  callTime?: string;
  consumeToken?: number; // 消耗token
  inputContent?: string;
  outputContent?: string;
  responseDuration?: number; // 响应时长，单位：ms
}

const basePath = 'yxt-basis';

// 1.创建提示词模版
export async function createPromptTemplateApi(data: Partial<PromptTemplate>) {
  return requestClient.post(
    `${basePath}/b/promptTemplate/w/1.0/createPromptTemplate`,
    data,
    {
      baseURL: merApiURL,
    },
  );
}

// 2.更新提示词模版
export async function updatePromptTemplateApi(data: Partial<PromptTemplate>) {
  return requestClient.post(
    `${basePath}/b/promptTemplate/w/1.0/updatePromptTemplate`,
    data,
    {
      baseURL: merApiURL,
    },
  );
}

// 3.删除提示词模版
export async function deletePromptTemplateApi(data: {
  promptTemplateNo: string;
}) {
  return requestClient.post(
    `${basePath}/b/promptTemplate/w/1.0/deletePromptTemplate`,
    data,
    {
      baseURL: merApiURL,
    },
  );
}

//  4.回滚提示词模版版本内容
export async function rollBackPromptTemplateVersionApi(data: {
  dataVersion: number;
  promptTemplateNo: string;
}) {
  return requestClient.post(
    `${basePath}/b/promptTemplate/w/1.0/rollBackPromptTemplateVersion`,
    data,
    {
      baseURL: merApiURL,
    },
  );
}

// 5.根据提示词模版编号查询模版详情
export async function getPromptTemplateByNoApi(data: {
  promptTemplateNo: string;
}) {
  return requestClient.post(
    `${basePath}/b/promptTemplate/r/1.0/getPromptTemplateByNo`,
    data,
    {
      baseURL: merApiURL,
    },
  );
}

// 6.查询提示词模版调用记录详情
export async function getPromptTemplateCallRecordApi(data: {
  callRecordNo: string;
}) {
  return requestClient.post(
    `${basePath}/b/promptTemplate/r/1.0/getPromptTemplateCallRecord`,
    data,
    {
      baseURL: merApiURL,
    },
  );
}

// 7.查询提示词模版调用统计
export async function getPromptTemplateCallStatsApi(data: {
  promptTemplateNo: string;
}) {
  return requestClient.post(
    `${basePath}/b/promptTemplate/r/1.0/getPromptTemplateCallStats`,
    data,
    {
      baseURL: merApiURL,
    },
  );
}

// 8.查询提示词模版版本内容
export async function getPromptTemplateVersionApi(data: {
  dataVersion: number;
  promptTemplateNo: string;
}) {
  return requestClient.post(
    `${basePath}/b/promptTemplate/r/1.0/getPromptTemplateVersion`,
    data,
    {
      baseURL: merApiURL,
    },
  );
}

// 9.分页查询提示词模版列表
export async function pagePromptTemplateApi(data: {
  currentPage: number;
  cursor?: string;
  pageSize: number;
  pageType?: string;
  promptTemplateDesc?: string;
}) {
  return requestClient.post(
    `${basePath}/b/promptTemplate/r/1.0/pagePromptTemplate`,
    data,
    {
      baseURL: merApiURL,
    },
  );
}

// 10.分页查询提示词模版调用记录列表
export async function pagePromptTemplateCallRecordApi(data: {
  callTime?: string;
  currentPage: number;
  cursor?: string;
  pageSize: number;
  pageType?: string;
  promptTemplateNo: string;
  recordDimension?: string;
}) {
  return requestClient.post(
    `${basePath}/b/promptTemplate/r/1.0/pagePromptTemplateCallRecord`,
    data,
    {
      baseURL: merApiURL,
    },
  );
}

// 11.分页查询提示词模版版本历史列表
export async function pagePromptTemplateVersionApi(data: {
  currentPage: number;
  cursor?: string;
  pageSize: number;
  pageType?: string;
  promptTemplateNo: string;
}) {
  return requestClient.post(
    `${basePath}/b/promptTemplate/r/1.0/pagePromptTemplateVersion`,
    data,
    {
      baseURL: merApiURL,
    },
  );
}
