/*
 * @Author: <PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-07-16 13:45:36
 * @LastEditors: zhangqian <EMAIL>
 * @LastEditTime: 2025-07-23 16:52:55
 * @Description: 页面功能描述，例如：用户列表、商品详情等
 * @FilePath: /dev-platform-ui/apps/web-ele/src/api/mcp.ts
 */
import { requestClient } from '#/api/request';

import { merApiURL } from './core/auth';
import { mockPageQueryApiApi } from './mock/apiData';

const basePath = 'yxt-mcp-manager-server';

// 开发环境使用mock数据的开关
const USE_MOCK_DATA = true; // 设置为 false 使用真实API

// MCP工具定义
export interface McpTool {
  id: string;
  toolKey: string;
  toolName: string;
  apiMethod: string;
  apiUrl: string;
  apiName: string;
  apiService: string;
  toolAppNameList: string[];
  toolStatus: 'disabled' | 'enabled' | 'maintenance';
  createdAt?: string;
  updatedAt?: string;
}

// 第一部分： MCP工具接口相关 共计9个

// 1.1 分页查询MCP工具
export async function pageQueryMcpToolApi(data: {
  currentPage: number;
  cursor?: string;
  mcpAppId?: string; // 应用appId
  pageSize: number;
  pageType?: string;
  toolKey?: string; // 工具key
  toolStatus?: string; // 工具状态
}) {
  return requestClient.post(
    `${basePath}/b/mcp/tool/r/1.0/pageQueryMcpTool`,
    data,
    {
      baseURL: merApiURL,
    },
  );
}

// 1.2 分页查询业务API
export async function pageQueryApiApi(data: {
  currentPage: number;
  cursor?: string;
  pageSize: number;
  pageType?: string;
  searchKey?: string; // API名称或路径
}) {
  // 如果启用mock数据，使用mock函数
  if (USE_MOCK_DATA) {
    return mockPageQueryApiApi({
      currentPage: data.currentPage,
      pageSize: data.pageSize,
      searchKey: data.searchKey,
    });
  }

  // 否则使用真实API
  return requestClient.post(`${basePath}/b/mcp/tool/r/1.0/pageQueryApi`, data, {
    baseURL: merApiURL,
  });
}

// 1.3 创建Mcp工具信息
export async function createMcpToolApi(data: {
  apiMethod: string; // API请求方式
  apiName: string; // API名称
  apiService: string; // API服务
  apiUrl: string; // API路径
  mcpAppId: string; // 应用appId
  toolAppNameList: string[]; // 工具应用名称列表
  toolDescription: string; // 工具描述
  toolKey: string; // 工具key
  toolName: string; // 工具名称
}) {
  return requestClient.post(
    `${basePath}/b/mcp/tool/w/1.0/createMcpTool`,
    data,
    {
      baseURL: merApiURL,
    },
  );
}

// 1.4 修改Mcp工具信息
export async function updateMcpToolApi(data: {
  apiMethod: string; // API请求方式
  apiName: string; // API名称
  apiService: string; // API服务
  apiUrl: string; // API路径
  mcpAppId: string; // 应用appId
  toolAppNameList: string[]; // 工具应用名称列表
  toolDescription: string; // 工具描述
  toolId: string; // 工具id
  toolKey: string; // 工具key
  toolName: string; // 工具名称
}) {
  return requestClient.post(
    `${basePath}/b/mcp/tool/w/1.0/updateMcpTool`,
    data,
    {
      baseURL: merApiURL,
    },
  );
}

// 1.5 预览Mcp工具信息
export async function previewMcpToolApi(data: {
  toolKey: string; // 工具id
}) {
  return requestClient.post(
    `${basePath}/b/mcp/tool/w/1.0/previewMcpTool`,
    data,
    {
      baseURL: merApiURL,
    },
  );
}

// 1.6 下线Mcp工具信息
export async function offLineMcpToolApi(data: {
  toolId: string; // 工具id
}) {
  return requestClient.post(
    `${basePath}/b/mcp/tool/w/1.0/offLineMcpTool`,
    data,
    {
      baseURL: merApiURL,
    },
  );
}

// 1.7 发布上线Mcp工具信息
export async function onLineMcpToolApi(data: {
  toolId: string; // 工具id
}) {
  return requestClient.post(
    `${basePath}/b/mcp/tool/w/1.0/onLineMcpTool`,
    data,
    {
      baseURL: merApiURL,
    },
  );
}

// 1.8 分页查询API工具配置
export async function pageQueryApiToolConfigApi(data: {
  apiService?: string; // 接口所属服务
  apiUrlList: string[]; // 接口Url集合
  currentPage: number;
  cursor?: string;
  pageSize: number;
  pageType?: string;
}) {
  return requestClient.post(
    `${basePath}/b/mcp/tool/r/1.0/pageQueryMcpToolConfig`,
    data,
    {
      baseURL: merApiURL,
    },
  );
}

// 1.9 查询MCP工具配置
export async function getMcpToolConfigApi(data: {
  toolKey: string; // 工具id
}) {
  return requestClient.post(
    `${basePath}/b/mcp/tool/r/1.0/getMcpToolConfig`,
    data,
    {
      baseURL: merApiURL,
    },
  );
}

// 第二部分 mcp应用接口相关

// 2.1 修改McpApp信息
export async function updateMcpAppApi(data: {
  appDesc: number;
  appId: string;
  appName: string;
  authType: string;
  toolList: string[];
}) {
  return requestClient.post(`${basePath}/b/mcp/app/w/1.0/updateMcpApp`, data, {
    baseURL: merApiURL,
  });
}

// 2.2 生成McpAppId
export async function generateMcpAppIdApi() {
  return requestClient.get(`${basePath}/b/mcp/app/w/1.0/generateMcpAppId`, {
    baseURL: merApiURL,
  });
}

// 2.3 创建McpApp信息
export async function createMcpAppApi(data: {
  appDesc: number;
  appName: string;
  authType: string;
  toolList: string[];
}) {
  return requestClient.post(`${basePath}/b/mcp/app/w/1.0/createMcpApp`, data, {
    baseURL: merApiURL,
  });
}

// 2.4 查询MCP工具配置
export async function pageQueryMcpToolAppApi(data: {
  currentPage: number;
  cursor?: string;
  pageSize: number;
  pageType?: string;
  toolKey?: string;
}) {
  return requestClient.post(
    `${basePath}/b/mcp/app/r/1.0/pageQueryMcpTool`,
    data,
    {
      baseURL: merApiURL,
    },
  );
}

// 2.5 分页查询业务API
export async function pageQueryMcpAppApi(data: {
  currentPage: number;
  cursor?: string;
  pageSize: number;
  pageType?: string;
  searchKey?: string; // API名称或路径
}) {
  return requestClient.post(
    `${basePath}/b/mcp/app/r/1.0/pageQueryMcpApp`,
    data,
    {
      baseURL: merApiURL,
    },
  );
}

// 2.6 查询MCP工具配置
export async function getMcpAppApi(data: { appId: string }) {
  return requestClient.post(`${basePath}/b/mcp/app/r/1.0/getMcpApp`, data, {
    baseURL: merApiURL,
  });
}
