import { baseRequestClient, requestClient } from '#/api/request';

export const merApiURL = import.meta.env.VITE_MER_API_URL;

export namespace AuthApi {
  /** 登录接口参数 */
  export interface LoginParams {
    loginSourceType?: number;
    clientId?: string;
    pwd?: string;
    account?: string;
    imgVerificationCode?: string;
    verificationCode?: string;
  }

  /** 登录接口返回值 */
  export interface LoginResult {
    token: string;
    // "token": "",
    // "userName": "1156478",
    // "userId": "4089125393368354210",
    // "merLogo": "500001/********/f736dd0268f343549f0b157da1fc4a30.png",
    // "merName": "一心堂测试",
    // "fromChannel": 1,
    // "updatePwd": 0,
    // "expire": 86400,
    // "merCode": "500001",
    // "mobile": "***********"
  }

  export interface RefreshTokenResult {
    data: string;
    status: number;
  }
}

/**
 * 登录
 */
export async function loginApi(data: AuthApi.LoginParams) {
  return requestClient.post<AuthApi.LoginResult>(
    '/yxt-login/b/token/w/1.0/login',
    {
      ...data,
      loginSourceType: 1,
    },
    {
      baseURL: merApiURL,
    },
  );
}

/**
 * 刷新accessToken
 */
export async function refreshTokenApi() {
  return baseRequestClient.post<AuthApi.RefreshTokenResult>('/auth/refresh', {
    withCredentials: true,
  });
}

/**
 * 退出登录
 */
export async function logoutApi() {
  return requestClient.post(
    '/yxt-login/b/token/w/1.0/logout',
    {},
    {
      baseURL: merApiURL,
      withCredentials: true,
    },
  );
}

/**
 * 获取用户权限码
 */
export async function getAccessCodesApi() {
  // return requestClient.get<string[]>('/auth/codes');
  return [];
}
