import { requestClient } from '#/api/request';

// 根据枚举名称查询枚举集合信息
export async function getAppsApi(
  params: any = {},
  currentPage: number = 1,
  pageSize: number = 10,
) {
  return requestClient
    .get('/hera/safe-center/b/safeConfig/page', {
      params: {
        currentPage,
        pageSize,
        ...params,
      },
    })
    .then((res) => {
      return {
        total: res.totalCount,
        items: res.data,
      }; // 返回响应数据
    });
}
