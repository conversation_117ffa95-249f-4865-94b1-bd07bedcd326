import type { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
  {
    meta: {
      order: 1,
      title: '下载中心',
      icon: 'lucide:monitor-down',
    },
    name: 'download',
    path: '/download',
    redirect: '/download/project',
    children: [
      {
        name: 'project',
        path: 'project',
        component: () => import('#/views/download/project/index.vue'),
        meta: {
          icon: 'lucide:layout-dashboard',
          title: '项目管理',
          hideInMenu: true,
          activePath: '/download',
        },
      },
      {
        name: 'scene',
        path: 'scene',
        component: () => import('#/views/download/scene/index.vue'),
        meta: {
          icon: 'lucide:rectangle-horizontal',
          title: '场景管理',
          hideInMenu: true,
          activePath: '/download',
        },
      },
    ],
  },
];

export default routes;
