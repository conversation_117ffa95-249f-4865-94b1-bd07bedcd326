import type { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
  // {
  //   meta: {
  //     order: -2,
  //     title: $t('system.title'),
  //     icon: 'lucide:monitor-smartphone',
  //   },
  //   name: 'systemManage',
  //   path: '/systemManage',
  //   children: [
  //     {
  //       name: 'interior',
  //       path: '/systemManage/interior',
  //       component: () => import('#/views/system/interior/index.vue'),
  //       meta: {
  //         icon: 'lucide:file-key',
  //         title: $t('system.interior'),
  //       },
  //     },
  //     {
  //       name: 'external',
  //       path: '/systemManage/external',
  //       component: () => import('#/views/system/external/index.vue'),
  //       meta: {
  //         icon: 'lucide:folder-key',
  //         title: $t('system.external'),
  //       },
  //     },
  //     {
  //       name: 'audit',
  //       path: '/systemManage/audit',
  //       component: () => import('#/views/system/audit/index.vue'),
  //       meta: {
  //         icon: 'lucide:square-mouse-pointer',
  //         title: $t('system.audit'),
  //       },
  //     },
  //   ],
  // },
];

export default routes;
