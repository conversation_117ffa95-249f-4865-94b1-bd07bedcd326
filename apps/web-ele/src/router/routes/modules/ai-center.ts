import type { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
  {
    meta: {
      order: 3,
      title: 'AI中台',
      icon: 'lucide:brain',
    },
    name: 'ai<PERSON><PERSON>',
    path: '/ai-center',
    children: [
      // {
      //   meta: {
      //     title: 'MCP管理',
      //     icon: 'lucide:layout-grid',
      //   },
      //   name: 'mcpManage',
      //   path: '/mcp',
      //   children: [
      //     {
      //       name: 'tool',
      //       path: '/tool',
      //       meta: {
      //         icon: 'lucide:rows-3',
      //         title: $t('mcp-center.tool.title'),
      //       },
      //       children: [
      //         {
      //           name: 'tool-add',
      //           path: '/mcp/tool/add',
      //           component: () => import('#/views/mcp/tool/tool-add/index.vue'),
      //           meta: {
      //             maxNumOfOpenTab: 1,
      //             title: '添加API工具',
      //           },
      //         },
      //         {
      //           name: 'tool-list',
      //           path: '/mcp/tool/list',
      //           component: () => import('#/views/mcp/tool/list.vue'),
      //           meta: {
      //             maxNumOfOpenTab: 1,
      //             title: 'API工具列表',
      //           },
      //         },
      //       ],
      //     },
      //     {
      //       name: 'config',
      //       path: '/config',
      //       meta: {
      //         icon: 'lucide:settings',
      //         title: $t('mcp-center.config.title'),
      //         // hideChildrenInMenu: true,
      //       },
      //       children: [
      //         {
      //           name: 'config-index',
      //           path: '/mcp/config/index',
      //           component: () => import('#/views/mcp/config/index.vue'),
      //           meta: {
      //             // activePath: '/mcp/config',
      //             maxNumOfOpenTab: 1,
      //             title: '应用授权',
      //           },
      //         },
      //         // {
      //         //   name: 'parameter-converter',
      //         //   path: '/mcp/config/parameter',
      //         //   component: () => import('#/views/mcp/config/parameter.vue'),
      //         //   meta: {
      //         //     maxNumOfOpenTab: 1,
      //         //     title: '参数转换器',
      //         //   },
      //         // },
      //       ],
      //     },
      //     // {
      //     //   name: 'monitoring',
      //     //   path: '/monitoring',
      //     //   meta: {
      //     //     icon: 'lucide:monitor',
      //     //     title: $t('mcp-center.monitoring.title'),
      //     //     // hideChildrenInMenu: true,
      //     //   },
      //     //   children: [
      //     //     {
      //     //       name: 'monitoring-index',
      //     //       path: '/mcp/monitoring/index',
      //     //       component: () => import('#/views/mcp/monitoring/index.vue'),
      //     //       meta: {
      //     //         maxNumOfOpenTab: 1,
      //     //         title: '调用监控',
      //     //       },
      //     //     },
      //     //     // {
      //     //     //   name: 'setting-index',
      //     //     //   path: '/mcp/monitoring/setting',
      //     //     //   component: () => import('#/views/mcp/monitoring/setting.vue'),
      //     //     //   meta: {
      //     //     //     maxNumOfOpenTab: 1,
      //     //     //     title: '服务器设置',
      //     //     //   },
      //     //     // },
      //     //   ],
      //     // },
      //   ],
      // },
      {
        meta: {
          title: 'Prompt模板管理',
          icon: 'lucide:rectangle-horizontal',
        },
        name: 'prompt',
        path: '/prompt',
        redirect: '/prompt/list',
        children: [
          {
            name: 'list',
            path: '/prompt/list',
            component: () => import('#/views/prompt/list.vue'),
            meta: {
              title: '模板列表',
              hideInMenu: true,
              activePath: '/prompt',
            },
          },
          {
            name: 'add',
            path: '/prompt/add',
            component: () => import('#/views/prompt/add.vue'),
            meta: {
              title: '创建模板',
              hideInMenu: true,
              activePath: '/prompt',
              maxNumOfOpenTab: 1,
            },
          },
          {
            name: 'history',
            path: '/prompt/history',
            component: () => import('#/views/prompt/history.vue'),
            meta: {
              title: '版本历史',
              hideInMenu: true,
              activePath: '/prompt',
              maxNumOfOpenTab: 1,
            },
          },
          {
            name: 'record',
            path: '/prompt/record',
            component: () => import('#/views/prompt/record.vue'),
            meta: {
              title: '调用记录',
              hideInMenu: true,
              activePath: '/prompt',
              maxNumOfOpenTab: 1,
            },
          },
        ],
      },
    ],
  },
];

export default routes;
