import type { RouteRecordRaw } from 'vue-router';

import { $t } from '#/locales';

const routes: RouteRecordRaw[] = [
  {
    meta: {
      order: -3,
      title: $t('port.title'),
      icon: 'lucide:chart-network',
    },
    name: 'portManage',
    path: '/console',
    children: [
      {
        name: 'apis',
        path: 'apis',
        component: () => import('#/views/port/list/index.vue'),
        meta: {
          icon: 'lucide:rows-4',
          title: $t('port.list'),
        },
      },
      {
        name: 'apps',
        path: 'apps',

        meta: {
          icon: 'lucide:layout-grid',
          title: $t('port.app'),
          hideChildrenInMenu: true,
        },
        children: [
          {
            name: 'apps-list',
            path: '',

            meta: {
              activePath: '/console/apps',
              title: $t('port.app'),
            },
            children: [
              {
                name: 'apps-detail-index',
                path: ':',
                component: () => import('#/views/apps/index.vue'),
                meta: {
                  activePath: '/console/apps',
                  maxNumOfOpenTab: 1,
                  title: '应用列表',
                },
              },
              {
                name: 'apps-detail',
                path: ':appKey',
                component: () => import('#/views/apps/detail.vue'),
                meta: {
                  activePath: '/console/apps',
                  maxNumOfOpenTab: 1,
                  title: '应用详情',
                },
              },
            ],
          },
        ],
      },
    ],
  },
];

export default routes;
