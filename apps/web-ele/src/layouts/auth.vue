<script lang="ts" setup>
import { computed } from 'vue';

import { AuthPageLayout } from '@vben/layouts';
import { preferences } from '@vben/preferences';

// const appName = computed(() => preferences.app.name);
const logo = computed(() => preferences.logo.source);
</script>

<template>
  <AuthPageLayout
    app-name="心云 DEV-PLATFORM"
    :logo="logo"
    page-description=""
    page-title="心云基础运维平台"
    :toolbar-list="['layout']"
  >
    <!-- :toolbarList="['color', 'language', 'layout', 'theme']" -->
    <!-- 自定义工具栏 -->
    <!-- <template #toolbar></template> -->
  </AuthPageLayout>
</template>
