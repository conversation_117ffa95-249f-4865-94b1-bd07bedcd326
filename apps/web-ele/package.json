{"name": "@vben/web-ele", "version": "0.0.1-beta.3", "homepage": "https://vben.pro", "bugs": "https://github.com/vbenjs/vue-vben-admin/issues", "repository": {"type": "git", "url": "git+https://github.com/vbenjs/vue-vben-admin.git", "directory": "apps/web-ele"}, "license": "MIT", "author": {"name": "vben", "email": "<EMAIL>", "url": "https://github.com/anncwb"}, "type": "module", "scripts": {"build": "pnpm vite build --mode=`node ../../scripts/env.js`", "build:analyze": "pnpm vite build --mode analyze", "dev": "pnpm vite --mode development", "preview": "vite preview", "typecheck": "vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>"}, "imports": {"#/*": "./src/*"}, "dependencies": {"@element-plus/icons-vue": "catalog:", "@vben/access": "workspace:*", "@vben/common-ui": "workspace:*", "@vben/constants": "workspace:*", "@vben/hooks": "workspace:*", "@vben/icons": "workspace:*", "@vben/layouts": "workspace:*", "@vben/locales": "workspace:*", "@vben/plugins": "workspace:*", "@vben/preferences": "workspace:*", "@vben/request": "workspace:*", "@vben/stores": "workspace:*", "@vben/styles": "workspace:*", "@vben/types": "workspace:*", "@vben/utils": "workspace:*", "@vueuse/core": "catalog:", "axios": "^1.9.0", "dayjs": "catalog:", "element-plus": "catalog:", "md5": "^2.3.0", "pinia": "catalog:", "vue": "catalog:", "vue-router": "catalog:", "zod": "^3.24.2"}, "devDependencies": {"@types/md5": "^2.3.5", "unplugin-element-plus": "catalog:", "unplugin-vue-components": "^28.0.0"}}