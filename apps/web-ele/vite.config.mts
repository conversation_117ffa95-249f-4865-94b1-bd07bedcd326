import { defineConfig } from '@vben/vite-config';

import ElementPlus from 'unplugin-element-plus/vite';
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers';
import Components from 'unplugin-vue-components/vite';

export default defineConfig(async () => {
  return {
    application: {},
    vite: {
      build: {
        emptyOutDir: true,
        outDir: '../../dist',
      },
      plugins: [
        ElementPlus({
          format: 'esm',
        }),
        // 自动按需引入PlusProComponents
        Components({
          resolvers: [ElementPlusResolver({ importStyle: 'sass' })],
        }),
      ],
      server: {
        proxy: {
          '/merchants': {
            changeOrigin: true,
            cookieDomainRewrite: '',
            rewrite: (path) => path.replace(/^\/merchants/, ''),
            // mock代理目标地址
            target: 'https://dev-merchants.hxyxt.com/businesses-gateway',
            ws: true,
          },
          '/proxy': {
            changeOrigin: true,
            cookieDomainRewrite: '',
            rewrite: (path) => path.replace(/^\/proxy/, ''),
            // mock代理目标地址
            target: 'https://dev-api.hxyxt.com',
            ws: true,
          },
        },
      },
    },
  };
});
