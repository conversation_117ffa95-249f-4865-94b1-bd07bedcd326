{"name": "@vben/access", "version": "5.5.3", "homepage": "https://github.com/vbenjs/vue-vben-admin", "bugs": "https://github.com/vbenjs/vue-vben-admin/issues", "repository": {"type": "git", "url": "git+https://github.com/vbenjs/vue-vben-admin.git", "directory": "packages/effects/permissions"}, "license": "MIT", "type": "module", "sideEffects": ["**/*.css"], "exports": {".": {"types": "./src/index.ts", "default": "./src/index.ts"}}, "dependencies": {"@vben/preferences": "workspace:*", "@vben/stores": "workspace:*", "@vben/types": "workspace:*", "@vben/utils": "workspace:*", "vue": "catalog:"}}